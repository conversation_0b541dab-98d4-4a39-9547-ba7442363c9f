// OpenAI API服务实现
// 用于调用GPT-4o模型生成故事和图片

import apiKeyManager from './apiKeyManager.js';
import {
  STORY_PROMPT_TEMPLATE,
  IMAGE_PROMPT_TEMPLATE,
  INTERACTIVE_QUESTION_PROMPT_TEMPLATE,
  PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE,
  AUTISM_PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE
} from './promptTemplates.js';

class OpenAIService {
  constructor() {
    this.baseUrl = 'https://api.openai.com/v1';
    this.modelName = 'gpt-4o';
    this.imageModelName = 'dall-e-3';
  }

  // 获取请求头
  getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKeyManager.getApiKey()}`
    };
  }

  // 生成故事内容
  async generateStory(ageRange, theme) {
    try {
      if (!apiKeyManager.isInitialized()) {
        throw new Error('API密钥未初始化');
      }

      const prompt = STORY_PROMPT_TEMPLATE
        .replace('{age_range}', ageRange)
        .replace('{theme}', theme);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            { role: 'system', content: '你是一位专业的儿童绘本作家，擅长为自闭症儿童创作故事。' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 2000
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`OpenAI API错误: ${error.error?.message || '未知错误'}`);
      }

      const data = await response.json();
      const storyContent = data.choices[0].message.content;
      
      // 尝试解析JSON格式的故事内容
      try {
        return JSON.parse(storyContent);
      } catch (e) {
        console.error('故事内容解析失败，返回原始文本', e);
        return { raw: storyContent };
      }
    } catch (error) {
      console.error('生成故事失败:', error);
      throw error;
    }
  }

  // 生成图片
  async generateImage(sceneDescription, ageRange) {
    try {
      if (!apiKeyManager.isInitialized()) {
        throw new Error('API密钥未初始化');
      }

      const prompt = IMAGE_PROMPT_TEMPLATE
        .replace('{scene_description}', sceneDescription)
        .replace('{age_range}', ageRange);

      const response = await fetch(`${this.baseUrl}/images/generations`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          model: this.imageModelName,
          prompt: prompt,
          n: 1,
          size: '1024x1024',
          quality: 'standard',
          response_format: 'url'
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`OpenAI API错误: ${error.error?.message || '未知错误'}`);
      }

      const data = await response.json();
      return data.data[0].url;
    } catch (error) {
      console.error('生成图片失败:', error);
      throw error;
    }
  }

  // 生成交互问题
  async generateInteractiveQuestion(ageRange, theme, storyContext, context) {
    try {
      if (!apiKeyManager.isInitialized()) {
        throw new Error('API密钥未初始化');
      }

      const prompt = INTERACTIVE_QUESTION_PROMPT_TEMPLATE
        .replace('{age_range}', ageRange)
        .replace('{theme}', theme)
        .replace('{story_context}', storyContext)
        .replace('{context}', context);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            { role: 'system', content: '你是一位专业的儿童教育专家，擅长设计适合自闭症儿童的互动问题。' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 500
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`OpenAI API错误: ${error.error?.message || '未知错误'}`);
      }

      const data = await response.json();
      const questionContent = data.choices[0].message.content;
      
      // 尝试解析JSON格式的问题内容
      try {
        return JSON.parse(questionContent);
      } catch (e) {
        console.error('问题内容解析失败，返回原始文本', e);
        return { 
          question: questionContent,
          guidance_prompt: '请尝试回答问题，如果不确定，可以描述你的想法。'
        };
      }
    } catch (error) {
      console.error('生成交互问题失败:', error);
      throw error;
    }
  }

  // 分析用户表现
  async analyzePerformance(questions, answers) {
    try {
      if (!apiKeyManager.isInitialized()) {
        throw new Error('API密钥未初始化');
      }

      if (questions.length !== answers.length || questions.length === 0) {
        throw new Error('问题和回答数量不匹配或为空');
      }

      let prompt = PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE;
      
      // 替换问题和回答
      for (let i = 0; i < questions.length; i++) {
        prompt = prompt
          .replace(`{question${i+1}}`, questions[i])
          .replace(`{answer${i+1}}`, answers[i]);
      }

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            { role: 'system', content: '你是一位专业的儿童心理学家，擅长评估自闭症儿童的语言和社交能力。' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.3,
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`OpenAI API错误: ${error.error?.message || '未知错误'}`);
      }

      const data = await response.json();
      const analysisContent = data.choices[0].message.content;
      
      // 尝试解析JSON格式的分析内容
      try {
        // 清理分析内容，移除可能的代码块标记
        let cleanedContent = analysisContent.trim();

        // 移除```json和```标记
        if (cleanedContent.startsWith('```json')) {
          cleanedContent = cleanedContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanedContent.startsWith('```')) {
          cleanedContent = cleanedContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        console.log('🧹 基础分析清理后的内容长度:', cleanedContent.length);

        return JSON.parse(cleanedContent);
      } catch (e) {
        console.error('分析内容解析失败，返回原始文本', e);
        return { raw: analysisContent };
      }
    } catch (error) {
      console.error('分析用户表现失败:', error);
      throw error;
    }
  }

  // 专门的自闭症儿童分析方法（增强版，包含完整上下文）
  async analyzeAutismPerformanceWithContext(questions, answers, storyData, userResponses = []) {
    console.log('🎯 开始专业自闭症儿童分析（增强版）...');

    try {
      if (!apiKeyManager.isInitialized()) {
        throw new Error('API密钥未初始化');
      }

      if (questions.length !== answers.length || questions.length === 0) {
        throw new Error('问题和回答数量不匹配或为空');
      }

      // 构建完整的上下文信息
      const contextInfo = this.buildAnalysisContext(storyData, userResponses);

      // 使用增强版分析提示词模板
      let prompt = AUTISM_PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE;

      // 替换故事上下文信息
      prompt = prompt
        .replace('{story_title}', contextInfo.storyTitle)
        .replace('{story_theme}', contextInfo.storyTheme)
        .replace('{age_group}', contextInfo.ageGroup)
        .replace('{story_summary}', contextInfo.storySummary)
        .replace('{main_characters}', contextInfo.mainCharacters)
        .replace('{story_setting}', contextInfo.storySetting);

      // 替换问题和回答
      for (let i = 0; i < questions.length; i++) {
        prompt = prompt
          .replace(`{question${i+1}}`, questions[i])
          .replace(`{answer${i+1}}`, answers[i]);
      }

      console.log('📝 发送增强版专业分析请求到OpenAI...');
      console.log('📋 上下文信息长度:', prompt.length);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            {
              role: 'system',
              content: '你是一位专业的自闭症儿童康复专家和语言治疗师，具有丰富的临床经验和专业知识。你正在分析一个专门为自闭症儿童设计的AI交互式绘本系统中的儿童表现。请严格按照JSON格式输出分析结果，确保所有字段都完整填写。'
            },
            { role: 'user', content: prompt }
          ],
          temperature: 0.3,
          max_tokens: 4000 // 增加token限制以容纳更详细的分析
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`OpenAI API错误: ${error.error?.message || '未知错误'}`);
      }

      const data = await response.json();
      const analysisContent = data.choices[0].message.content;

      console.log('📊 OpenAI增强版分析结果长度:', analysisContent.length);
      console.log('📄 分析结果预览:', analysisContent.substring(0, 200) + '...');

      // 尝试解析JSON格式的分析结果
      try {
        // 清理分析内容，移除可能的代码块标记
        let cleanedContent = analysisContent.trim();

        // 移除```json和```标记
        if (cleanedContent.startsWith('```json')) {
          cleanedContent = cleanedContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanedContent.startsWith('```')) {
          cleanedContent = cleanedContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        console.log('🧹 清理后的内容长度:', cleanedContent.length);
        console.log('📄 清理后内容预览:', cleanedContent.substring(0, 200) + '...');

        const parsedResult = JSON.parse(cleanedContent);

        // 添加分析元数据
        parsedResult.analysis_metadata = {
          analysis_date: new Date().toISOString(),
          story_title: storyData?.title || '小熊波波的友谊之旅',
          story_theme: storyData?.theme || '友谊与社交',
          analysis_type: 'autism_specialized_enhanced',
          questions_count: questions.length,
          answers_provided: answers.filter(a => a && a.trim()).length,
          model_used: this.modelName,
          context_provided: true
        };

        console.log('✅ 增强版专业分析完成');
        return parsedResult;
      } catch (e) {
        console.error('❌ 增强版分析结果解析失败，回退到基础分析', e);
        console.log('原始内容前500字符:', analysisContent.substring(0, 500));

        // 回退到基础分析
        return await this.analyzePerformance(questions, answers);
      }
    } catch (error) {
      console.error('❌ 增强版分析失败:', error);

      // 回退到基础分析
      try {
        console.log('🔄 回退到基础分析方法...');
        return await this.analyzePerformance(questions, answers);
      } catch (fallbackError) {
        console.error('❌ 基础分析也失败了:', fallbackError);
        throw new Error('分析服务暂时不可用，请稍后重试');
      }
    }
  }

  // 构建分析上下文信息
  buildAnalysisContext(storyData, userResponses = []) {
    console.log('🔧 构建分析上下文信息...');

    // 提取故事基本信息
    const storyTitle = storyData?.title || '小熊波波的友谊之旅';
    const storyTheme = storyData?.theme || '友谊与社交技能';
    const ageGroup = storyData?.ageGroup || '6-8岁';

    // 构建故事概要
    let storySummary = '';
    if (storyData?.pages) {
      const nonInteractivePages = storyData.pages.filter(page => !page.isInteractive);
      storySummary = nonInteractivePages.slice(0, 3).map(page => page.content).join(' ');
      if (storySummary.length > 200) {
        storySummary = storySummary.substring(0, 200) + '...';
      }
    }
    if (!storySummary) {
      storySummary = '这是一个关于友谊和社交技能的温馨故事，主角通过各种冒险学习如何与他人相处。';
    }

    // 构建主要角色信息
    let mainCharacters = '';
    if (storyData?.characters && storyData.characters.length > 0) {
      mainCharacters = storyData.characters.map(char =>
        `${char.name}（${char.description}）`
      ).join('、');
    } else {
      mainCharacters = '小熊波波（主角，一只友善的小熊）、小兔莉莉（好朋友）、乌龟老师（智慧的引导者）';
    }

    // 构建故事背景
    const storySetting = storyData?.setting || '森林中的动物学校和游乐场，一个充满友爱和学习机会的环境';

    console.log('✅ 上下文信息构建完成:', {
      storyTitle,
      storyTheme,
      ageGroup,
      storySummaryLength: storySummary.length,
      mainCharacters: mainCharacters.substring(0, 100) + '...'
    });

    return {
      storyTitle,
      storyTheme,
      ageGroup,
      storySummary,
      mainCharacters,
      storySetting
    };
  }

  // 专门的自闭症儿童分析方法（保持向后兼容）
  async analyzeAutismPerformance(questions, answers, storyTheme = '友谊与社交') {
    console.log('🎯 开始专业自闭症儿童分析（兼容版）...');

    // 创建简化的故事数据用于上下文
    const simpleStoryData = {
      title: '小熊波波的友谊之旅',
      theme: storyTheme,
      ageGroup: '6-8岁'
    };

    // 调用增强版分析方法
    return await this.analyzeAutismPerformanceWithContext(questions, answers, simpleStoryData, []);
  }

  // 初始化API密钥
  initializeApiKey(apiKey) {
    apiKeyManager.initialize(apiKey);
  }

  // 检查API密钥是否已初始化
  isApiKeyInitialized() {
    return apiKeyManager.isInitialized();
  }

  // 清除API密钥
  clearApiKey() {
    apiKeyManager.clear();
  }
}

// 创建单例实例
const openAIService = new OpenAIService();

// 导出单例实例
export default openAIService;
