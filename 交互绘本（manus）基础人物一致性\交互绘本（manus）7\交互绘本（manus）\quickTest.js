// 快速测试修复后的API连接
console.log('🔧 测试API密钥管理器修复...\n');

// 模拟浏览器环境的import.meta.env
const mockEnv = {
  VITE_OPENAI_API_KEY: process.env.VITE_OPENAI_API_KEY || 'test-key'
};

console.log('环境变量检查:');
console.log('VITE_OPENAI_API_KEY:', mockEnv.VITE_OPENAI_API_KEY ? '✅ 已设置' : '❌ 未设置');

if (mockEnv.VITE_OPENAI_API_KEY && mockEnv.VITE_OPENAI_API_KEY !== 'test-key') {
  console.log('🔑 API密钥前缀:', mockEnv.VITE_OPENAI_API_KEY.substring(0, 15) + '...');
}

console.log('\n✅ 修复完成！现在可以在浏览器中测试故事生成功能了。');
console.log('\n📝 测试步骤:');
console.log('1. 打开 http://localhost:5173/');
console.log('2. 点击 "🎨 生成新故事"');
console.log('3. 选择一个主题');
console.log('4. 点击 "🎨 生成故事"');
console.log('5. 等待AI生成完成');
