/**
 * 增强功能测试脚本
 * 在浏览器控制台中运行此脚本来测试新的风格一致性功能
 */

// 测试增强功能的主函数
async function testEnhancedFeatures() {
    console.log('🧪 开始测试增强的风格一致性功能...');
    
    try {
        // 1. 测试风格管理器
        console.log('\n🎨 测试风格管理器...');
        
        // 动态导入模块（如果在模块环境中）
        let StyleManager, CharacterManager, enhancedIllustrationService;
        
        try {
            // 尝试从全局变量获取（如果已加载）
            if (window.StyleManager) {
                StyleManager = window.StyleManager;
                CharacterManager = window.CharacterManager;
                enhancedIllustrationService = window.enhancedIllustrationService;
            } else {
                console.log('⚠️ 模块未在全局作用域中，尝试其他方式...');
                
                // 检查是否有现有的插画生成功能
                if (window.generateIllustrationFromAnswer) {
                    console.log('✅ 找到现有的插画生成功能');
                    
                    // 测试现有功能
                    await testExistingIllustrationFunction();
                    return;
                }
            }
        } catch (error) {
            console.log('⚠️ 直接模块导入失败，使用现有功能测试:', error.message);
            await testExistingIllustrationFunction();
            return;
        }
        
        if (StyleManager) {
            const styleManager = new StyleManager();
            
            // 测试基础风格生成
            const stylePrompt = styleManager.generateStylePrompt("小熊波波在森林中玩耍");
            console.log('✅ 风格提示词生成成功:', stylePrompt.prompt.substring(0, 100) + '...');
            
            // 测试情绪调整
            const emotionStyle = styleManager.getEmotionStyleAdjustment("happy");
            console.log('✅ 情绪风格调整:', emotionStyle);
        }
        
        // 2. 测试角色管理器
        if (CharacterManager) {
            console.log('\n👥 测试角色管理器...');
            const characterManager = new CharacterManager();
            
            const characterDesc = characterManager.getCharacterDescription("小熊波波", "happy", "playing");
            console.log('✅ 角色描述生成:', characterDesc.substring(0, 100) + '...');
            
            const allCharacters = characterManager.getAllCharacters();
            console.log('✅ 可用角色:', allCharacters);
        }
        
        // 3. 测试增强插画服务
        if (enhancedIllustrationService) {
            console.log('\n🖼️ 测试增强插画服务...');
            
            // 设置测试故事上下文
            const testStoryContext = {
                title: "测试故事",
                theme: "友谊",
                characters: ["小熊波波", "小兔子"]
            };
            
            enhancedIllustrationService.setStoryContext(testStoryContext);
            console.log('✅ 故事上下文设置成功');
            
            // 获取统计信息
            const stats = enhancedIllustrationService.getGenerationStats();
            console.log('✅ 生成统计:', stats);
        }
        
        console.log('\n🎉 增强功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        console.log('🔄 尝试测试现有功能...');
        await testExistingIllustrationFunction();
    }
}

// 测试现有插画生成功能
async function testExistingIllustrationFunction() {
    console.log('\n🔍 测试现有插画生成功能...');
    
    try {
        // 检查LIBLIB服务状态
        if (window.liblibService) {
            console.log('✅ LIBLIB服务已加载');
            
            const apiStatus = window.liblibService.getApiStatus();
            console.log('📊 API状态:', apiStatus);
            
            if (apiStatus.isInitialized) {
                console.log('✅ API密钥已初始化');
                
                // 测试简单的图像生成
                try {
                    console.log('🎨 测试图像生成...');
                    const testPrompt = "小熊波波在森林中微笑，儿童插画风格";
                    
                    // 注意：这里只是测试API调用，实际生成可能需要时间
                    console.log('📝 测试提示词:', testPrompt);
                    console.log('⏳ 图像生成测试准备就绪（实际生成需要在交互页面进行）');
                    
                } catch (genError) {
                    console.log('⚠️ 图像生成测试跳过（需要在实际交互中进行）:', genError.message);
                }
            } else {
                console.log('⚠️ API密钥未初始化，请检查.env文件');
            }
        } else {
            console.log('⚠️ LIBLIB服务未找到');
        }
        
        // 检查其他关键服务
        const services = [
            'generateIllustrationFromAnswer',
            'checkStyleConsistency',
            'clearImageCache'
        ];
        
        services.forEach(serviceName => {
            if (window[serviceName]) {
                console.log(`✅ ${serviceName} 功能可用`);
            } else {
                console.log(`⚠️ ${serviceName} 功能未找到`);
            }
        });
        
    } catch (error) {
        console.error('❌ 现有功能测试失败:', error);
    }
}

// 测试API连接
async function testApiConnection() {
    console.log('\n🌐 测试API连接...');
    
    try {
        if (window.liblibService && window.liblibService.isApiKeyInitialized()) {
            console.log('🔑 API密钥已配置，测试连接...');
            
            const connectionTest = await window.liblibService.testConnection();
            
            if (connectionTest.success) {
                console.log('✅ API连接成功!');
                console.log('🖼️ 测试图像URL:', connectionTest.testImageUrl);
            } else {
                console.log('❌ API连接失败:', connectionTest.message);
            }
        } else {
            console.log('⚠️ API密钥未配置，请检查环境变量');
        }
    } catch (error) {
        console.error('❌ API连接测试失败:', error);
    }
}

// 显示测试指南
function showTestGuide() {
    console.log(`
🧪 增强功能测试指南
==================

1. 基础测试:
   testEnhancedFeatures()     - 测试所有增强功能
   
2. API测试:
   testApiConnection()        - 测试LIBLIB API连接
   
3. 实际出图测试:
   - 在应用中导航到交互页面（第4、8、11页）
   - 输入回答，观察插图生成
   - 检查控制台日志，查看增强功能是否启用
   
4. 监控功能:
   - 打开浏览器开发者工具
   - 查看Console标签页的详细日志
   - 观察Network标签页的API请求

5. 故事生成测试:
   - 点击"生成新故事"按钮
   - 选择不同主题测试
   - 观察生成的故事结构

开始测试: testEnhancedFeatures()
`);
}

// 将函数添加到全局作用域
window.testEnhancedFeatures = testEnhancedFeatures;
window.testApiConnection = testApiConnection;
window.showTestGuide = showTestGuide;

// 自动显示测试指南
showTestGuide();
