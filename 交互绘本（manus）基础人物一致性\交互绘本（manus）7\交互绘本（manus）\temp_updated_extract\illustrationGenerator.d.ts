// 为illustrationGenerator.js创建TypeScript声明文件

/**
 * 从用户回答中提取关键内容
 * @param {string} answer - 用户的回答内容
 * @returns {Object} 提取的关键内容
 */
export function extractKeyContent(answer: string): {
  characters: string[];
  actions: string[];
  emotions: string[];
  sceneElements: string[];
};

/**
 * 构建图像生成提示词
 * @param {string} answer - 用户的回答内容
 * @param {Object} context - 当前故事上下文
 * @returns {string} 完整的提示词
 */
export function buildImagePrompt(answer: string, context: any): string;

/**
 * 获取参考图像URL
 * @param {number} currentPageIndex - 当前页面索引
 * @param {Array} allImages - 所有可用的图像URL
 * @returns {Array} 参考图像URL数组
 */
export function getReferenceImages(currentPageIndex: number, allImages: Array<{pageIndex: number, url: string}>): string[];

/**
 * 调用OpenAI API生成图像
 * @param {string} prompt - 图像生成提示词
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<string>} 生成的图像URL
 */
export function generateImage(prompt: string, referenceImages?: string[]): Promise<string>;

/**
 * 保存生成的图像到本地缓存
 * @param {string} imageUrl - 生成的图像URL
 * @param {number} pageId - 页面ID
 * @returns {Promise<string>} 本地图像路径
 */
export function saveGeneratedImage(imageUrl: string, pageId: number): Promise<string>;

/**
 * 主函数：根据用户回答生成插画
 * @param {string} answer - 用户的回答内容
 * @param {number} pageId - 交互页面ID
 * @param {Object} context - 当前故事上下文
 * @param {Array} allImages - 所有可用的图像
 * @returns {Promise<string>} 生成的图像URL或路径
 */
export function generateIllustrationFromAnswer(
  answer: string, 
  pageId: number, 
  context: {
    currentPageIndex: number;
    currentPage: any;
  }, 
  allImages: Array<{pageIndex: number, url: string}>
): Promise<string>;

/**
 * 检查生成的插画是否与现有风格一致
 * @param {string} generatedImageUrl - 生成的图像URL
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<boolean>} 是否风格一致
 */
export function checkStyleConsistency(generatedImageUrl: string, referenceImages: string[]): Promise<boolean>;

export default {
  generateIllustrationFromAnswer,
  checkStyleConsistency,
  buildImagePrompt,
  extractKeyContent
};
