// 故事管理器
// 负责管理多个故事的存储、切换和持久化

import storyData from '../data/storyData';

class StoryManager {
  constructor() {
    this.stories = new Map();
    this.currentStoryId = null;
    this.storageKey = 'autism_storybook_stories';
    
    // 初始化默认故事
    this.initializeDefaultStory();
    
    // 从本地存储加载故事
    this.loadStoriesFromStorage();
  }

  // 初始化默认故事
  initializeDefaultStory() {
    const defaultStory = {
      id: 'default',
      ...storyData,
      isDefault: true,
      createdAt: new Date().toISOString()
    };
    
    this.stories.set('default', defaultStory);
    this.currentStoryId = 'default';
  }

  // 添加新故事
  addStory(storyData) {
    const storyId = this.generateStoryId(storyData.theme);
    const story = {
      id: storyId,
      ...storyData,
      isDefault: false,
      createdAt: new Date().toISOString()
    };
    
    this.stories.set(storyId, story);
    this.saveStoriesToStorage();
    
    console.log(`✅ 新故事已添加: ${story.title} (ID: ${storyId})`);
    return storyId;
  }

  // 生成故事ID
  generateStoryId(theme) {
    const timestamp = Date.now();
    const themeCode = this.getThemeCode(theme);
    return `${themeCode}_${timestamp}`;
  }

  // 获取主题代码
  getThemeCode(theme) {
    const codes = {
      '人际关系': 'interpersonal',
      '家庭生活': 'family',
      '法律常识': 'legal',
      '人伦道德': 'moral',
      '友谊': 'friendship'
    };
    return codes[theme] || 'custom';
  }

  // 获取当前故事
  getCurrentStory() {
    if (!this.currentStoryId || !this.stories.has(this.currentStoryId)) {
      this.currentStoryId = 'default';
    }
    return this.stories.get(this.currentStoryId);
  }

  // 切换到指定故事
  switchToStory(storyId) {
    if (this.stories.has(storyId)) {
      this.currentStoryId = storyId;
      this.saveStoriesToStorage();
      console.log(`📖 已切换到故事: ${this.stories.get(storyId).title}`);
      return true;
    }
    console.warn(`⚠️ 故事不存在: ${storyId}`);
    return false;
  }

  // 获取所有故事列表
  getAllStories() {
    return Array.from(this.stories.values()).sort((a, b) => {
      // 默认故事排在最前面
      if (a.isDefault) return -1;
      if (b.isDefault) return 1;
      // 其他故事按创建时间倒序
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }

  // 删除故事
  deleteStory(storyId) {
    if (storyId === 'default') {
      console.warn('⚠️ 不能删除默认故事');
      return false;
    }

    if (this.stories.has(storyId)) {
      const story = this.stories.get(storyId);
      this.stories.delete(storyId);
      
      // 如果删除的是当前故事，切换到默认故事
      if (this.currentStoryId === storyId) {
        this.currentStoryId = 'default';
      }
      
      this.saveStoriesToStorage();
      console.log(`🗑️ 已删除故事: ${story.title}`);
      return true;
    }
    
    return false;
  }

  // 获取故事统计信息
  getStoryStats() {
    const stories = this.getAllStories();
    const themes = {};
    
    stories.forEach(story => {
      if (!story.isDefault) {
        themes[story.theme] = (themes[story.theme] || 0) + 1;
      }
    });

    return {
      totalStories: stories.length,
      generatedStories: stories.length - 1, // 排除默认故事
      themeDistribution: themes,
      currentStory: this.getCurrentStory()?.title
    };
  }

  // 保存故事到本地存储
  saveStoriesToStorage() {
    try {
      const storiesData = {
        stories: Array.from(this.stories.entries()),
        currentStoryId: this.currentStoryId,
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem(this.storageKey, JSON.stringify(storiesData));
      console.log('💾 故事数据已保存到本地存储');
    } catch (error) {
      console.error('❌ 保存故事数据失败:', error);
    }
  }

  // 从本地存储加载故事
  loadStoriesFromStorage() {
    try {
      const savedData = localStorage.getItem(this.storageKey);
      if (!savedData) {
        console.log('📚 未找到保存的故事数据，使用默认设置');
        return;
      }

      const { stories, currentStoryId } = JSON.parse(savedData);
      
      // 恢复故事数据（排除默认故事，因为已经初始化了）
      stories.forEach(([id, story]) => {
        if (id !== 'default') {
          this.stories.set(id, story);
        }
      });

      // 恢复当前故事ID
      if (currentStoryId && this.stories.has(currentStoryId)) {
        this.currentStoryId = currentStoryId;
      }

      console.log(`📖 已从本地存储加载 ${this.stories.size} 个故事`);
    } catch (error) {
      console.error('❌ 加载故事数据失败:', error);
    }
  }

  // 清除所有生成的故事（保留默认故事）
  clearGeneratedStories() {
    const generatedStoryIds = Array.from(this.stories.keys()).filter(id => id !== 'default');
    
    generatedStoryIds.forEach(id => {
      this.stories.delete(id);
    });

    this.currentStoryId = 'default';
    this.saveStoriesToStorage();
    
    console.log(`🧹 已清除 ${generatedStoryIds.length} 个生成的故事`);
    return generatedStoryIds.length;
  }

  // 导出故事数据
  exportStory(storyId) {
    if (this.stories.has(storyId)) {
      const story = this.stories.get(storyId);
      const exportData = {
        ...story,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };
      
      return JSON.stringify(exportData, null, 2);
    }
    return null;
  }

  // 导入故事数据
  importStory(storyDataJson) {
    try {
      const storyData = JSON.parse(storyDataJson);
      
      // 验证故事数据结构
      if (!storyData.title || !storyData.pages || !Array.isArray(storyData.pages)) {
        throw new Error('故事数据格式不正确');
      }

      // 生成新的ID避免冲突
      const newStoryId = this.generateStoryId(storyData.theme || 'imported');
      const importedStory = {
        ...storyData,
        id: newStoryId,
        isDefault: false,
        createdAt: new Date().toISOString(),
        imported: true
      };

      this.stories.set(newStoryId, importedStory);
      this.saveStoriesToStorage();
      
      console.log(`📥 已导入故事: ${importedStory.title}`);
      return newStoryId;
    } catch (error) {
      console.error('❌ 导入故事失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const storyManager = new StoryManager();

export default storyManager;
