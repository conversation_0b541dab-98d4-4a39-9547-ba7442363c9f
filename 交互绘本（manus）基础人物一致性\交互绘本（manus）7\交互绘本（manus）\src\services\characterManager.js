/**
 * 人物特征管理器 - 确保绘本人物一致性
 * 基于 style control.txt 文档的策略实现
 * 注意：不涉及API调用，只负责角色特征管理和描述生成
 */

class CharacterManager {
    constructor() {
        // 主要角色特征库 - 基于小熊波波的友谊故事
        this.characters = {
            "小熊波波": {
                basicFeatures: {
                    species: "small brown bear cub",
                    size: "small, child-like proportions",
                    color: "warm brown fur",
                    eyes: "large, kind dark eyes",
                    nose: "small black nose",
                    ears: "round bear ears",
                    expression: "gentle, sometimes shy"
                },
                clothing: "simple, child-appropriate clothing",
                personalityTraits: "shy, gentle, curious",
                referenceImage: null,  // 将存储第一次生成的图像路径
                featureVector: null    // 存储特征向量用于一致性检查
            },
            "小兔子": {
                basicFeatures: {
                    species: "small white rabbit",
                    size: "small, energetic proportions",
                    color: "pure white fur",
                    eyes: "bright, friendly dark eyes",
                    nose: "small pink nose",
                    ears: "long rabbit ears",
                    expression: "cheerful, outgoing"
                },
                clothing: "simple, child-appropriate clothing",
                personalityTraits: "friendly, energetic, caring",
                referenceImage: null,
                featureVector: null
            },
            "小松鼠": {
                basicFeatures: {
                    species: "small gray squirrel",
                    size: "tiny, agile proportions",
                    color: "soft gray fur with white belly",
                    eyes: "bright, curious dark eyes",
                    nose: "small black nose",
                    ears: "pointed squirrel ears",
                    expression: "lively, intelligent"
                },
                clothing: "simple, child-appropriate clothing",
                personalityTraits: "clever, helpful, energetic",
                referenceImage: null,
                featureVector: null
            }
        };

        // 情绪表达映射
        this.emotionExpressions = {
            happy: "smiling brightly, joyful expression",
            sad: "slightly downcast, gentle sad expression",
            curious: "tilted head, interested expression",
            shy: "bashful expression, slightly hidden",
            excited: "energetic, enthusiastic expression",
            surprised: "wide eyes, surprised expression",
            worried: "concerned expression, furrowed brow",
            calm: "peaceful, relaxed expression",
            neutral: "default expression"
        };

        // 动作描述库
        this.actionDescriptions = {
            sitting: "sitting comfortably",
            standing: "standing upright",
            walking: "walking slowly",
            running: "running playfully",
            playing: "playing happily",
            talking: "talking with friends",
            listening: "listening carefully",
            thinking: "thinking thoughtfully",
            waving: "waving friendly",
            hugging: "giving a warm hug"
        };
    }

    /**
     * 获取角色的详细描述
     * @param {string} characterName - 角色名称
     * @param {string} emotion - 情绪状态
     * @param {string} action - 动作描述
     * @returns {string} 完整的角色描述
     */
    getCharacterDescription(characterName, emotion = "neutral", action = "") {
        if (!this.characters[characterName]) {
            console.warn(`未找到角色: ${characterName}`);
            return "";
        }

        const char = this.characters[characterName];
        const features = char.basicFeatures;

        // 组合基本特征
        const descriptionParts = [
            features.species,
            features.size,
            features.color,
            `with ${features.eyes}`,
            `and ${features.nose}`,
            features.ears
        ];

        // 添加情绪表达
        const emotionExpression = this.emotionExpressions[emotion] || 
                                 this.emotionExpressions.neutral || 
                                 features.expression;
        descriptionParts.push(emotionExpression);

        // 添加动作
        if (action) {
            const actionDesc = this.actionDescriptions[action] || action;
            descriptionParts.push(actionDesc);
        }

        // 添加服装
        descriptionParts.push(char.clothing);

        return descriptionParts.join(", ");
    }

    /**
     * 更新角色参考图像
     * @param {string} characterName - 角色名称
     * @param {string} imagePath - 图像路径
     */
    updateCharacterReference(characterName, imagePath) {
        if (this.characters[characterName]) {
            this.characters[characterName].referenceImage = imagePath;
            console.log(`已更新角色 ${characterName} 的参考图像: ${imagePath}`);
        }
    }

    /**
     * 获取角色参考图像
     * @param {string} characterName - 角色名称
     * @returns {string|null} 参考图像路径
     */
    getCharacterReference(characterName) {
        return this.characters[characterName]?.referenceImage || null;
    }

    /**
     * 检查角色是否有参考图像
     * @param {string} characterName - 角色名称
     * @returns {boolean} 是否有参考图像
     */
    hasCharacterReference(characterName) {
        return !!this.getCharacterReference(characterName);
    }

    /**
     * 获取所有角色列表
     * @returns {Array} 角色名称列表
     */
    getAllCharacters() {
        return Object.keys(this.characters);
    }

    /**
     * 获取角色的基本信息
     * @param {string} characterName - 角色名称
     * @returns {Object|null} 角色基本信息
     */
    getCharacterInfo(characterName) {
        return this.characters[characterName] || null;
    }

    /**
     * 为角色生成种子图像的特殊描述
     * @param {string} characterName - 角色名称
     * @returns {string} 种子图像描述
     */
    generateSeedImageDescription(characterName) {
        const baseDesc = this.getCharacterDescription(characterName, "neutral");
        
        // 添加种子图像特有的要求
        const seedRequirements = [
            "front view",
            "character sheet style",
            "neutral pose",
            "clear features",
            "reference image quality",
            "simple background"
        ];

        return `${baseDesc}, ${seedRequirements.join(", ")}`;
    }

    /**
     * 根据场景获取角色的适当情绪
     * @param {string} sceneType - 场景类型
     * @param {string} characterName - 角色名称
     * @returns {string} 推荐的情绪
     */
    getSceneAppropriateEmotion(sceneType, characterName) {
        const char = this.characters[characterName];
        if (!char) return "neutral";

        const sceneEmotions = {
            forest: char.personalityTraits.includes("curious") ? "curious" : "calm",
            playground: "happy",
            indoor: "calm",
            interaction: char.personalityTraits.includes("shy") ? "shy" : "neutral",
            home: "calm"
        };

        return sceneEmotions[sceneType] || "neutral";
    }

    /**
     * 生成多角色场景的描述
     * @param {Array} characters - 角色信息数组 [{name, emotion, action}]
     * @returns {string} 多角色场景描述
     */
    generateMultiCharacterDescription(characters) {
        if (!characters || characters.length === 0) {
            return "";
        }

        const descriptions = characters.map(charInfo => {
            const { name, emotion = "neutral", action = "" } = charInfo;
            return this.getCharacterDescription(name, emotion, action);
        });

        return descriptions.join(" and ");
    }

    /**
     * 获取角色一致性检查的关键特征
     * @param {string} characterName - 角色名称
     * @returns {Array} 关键特征列表
     */
    getCharacterKeyFeatures(characterName) {
        const char = this.characters[characterName];
        if (!char) return [];

        const features = char.basicFeatures;
        return [
            features.species,
            features.color,
            features.eyes,
            features.ears
        ];
    }
}

export default CharacterManager;
