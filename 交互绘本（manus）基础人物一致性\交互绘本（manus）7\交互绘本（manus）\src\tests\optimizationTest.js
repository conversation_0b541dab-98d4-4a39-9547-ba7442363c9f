/**
 * 优化功能测试 - 验证参考图像选择和页面特异性
 */

import enhancedIllustrationService from '../services/enhancedIllustrationService.js';
import storyIllustrationGenerator from '../services/storyIllustrationGenerator.js';

class OptimizationTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * 运行所有优化测试
     */
    async runAllTests() {
        console.log('🧪 开始运行优化功能测试...');
        
        try {
            await this.testReferenceImageSelection();
            await this.testPageSpecificContent();
            await this.testUniqueElementExtraction();
            await this.testPromptEnhancement();
            
            this.printTestResults();
        } catch (error) {
            console.error('❌ 测试运行失败:', error);
        }
    }

    /**
     * 测试参考图像选择逻辑
     */
    async testReferenceImageSelection() {
        console.log('\n🔍 测试参考图像选择逻辑...');
        
        const testCharacters = [
            { name: '小熊波波', emotion: 'happy', action: 'walking' }
        ];

        // 模拟生成历史
        enhancedIllustrationService.recordGenerationHistory({
            pageNumber: 1,
            imageUrl: 'https://example.com/page1.jpg',
            sceneType: 'forest',
            characters: testCharacters
        });

        enhancedIllustrationService.recordGenerationHistory({
            pageNumber: 2,
            imageUrl: 'https://example.com/page2.jpg',
            sceneType: 'home',
            characters: testCharacters
        });

        // 测试选择逻辑
        const selectedRef1 = enhancedIllustrationService.selectBestReferenceImage(testCharacters, 3, 'forest');
        const selectedRef2 = enhancedIllustrationService.selectBestReferenceImage(testCharacters, 3, 'home');

        this.assert(
            selectedRef1 !== null,
            '应该能选择到参考图像'
        );

        console.log('✅ 参考图像选择测试通过');
    }

    /**
     * 测试页面特定内容提取
     */
    async testPageSpecificContent() {
        console.log('\n📝 测试页面特定内容提取...');

        const testPage1 = {
            id: 1,
            content: '小熊波波走进了美丽的森林，看到了许多高大的树木和五颜六色的花朵。'
        };

        const testPage2 = {
            id: 2,
            content: '小兔子在家里画画，桌子上放着彩色的画笔和一本厚厚的画册。'
        };

        const analysis1 = storyIllustrationGenerator.analyzePageContent(testPage1, { theme: '友谊' });
        const analysis2 = storyIllustrationGenerator.analyzePageContent(testPage2, { theme: '友谊' });

        this.assert(
            analysis1.uniqueElements.length > 0,
            '第一页应该提取到特有元素'
        );

        this.assert(
            analysis2.uniqueElements.length > 0,
            '第二页应该提取到特有元素'
        );

        this.assert(
            JSON.stringify(analysis1.uniqueElements) !== JSON.stringify(analysis2.uniqueElements),
            '不同页面应该有不同的特有元素'
        );

        console.log('第1页特有元素:', analysis1.uniqueElements);
        console.log('第2页特有元素:', analysis2.uniqueElements);
        console.log('✅ 页面特定内容提取测试通过');
    }

    /**
     * 测试唯一元素提取
     */
    async testUniqueElementExtraction() {
        console.log('\n✨ 测试唯一元素提取...');

        const content1 = '小熊波波在森林里看到了美丽的花朵和高大的树木，太阳照耀着大地。';
        const content2 = '小兔子在家里的房间里画画，桌子上放着画笔和颜料。';

        const elements1 = storyIllustrationGenerator.extractUniqueElements(content1, 1);
        const elements2 = storyIllustrationGenerator.extractUniqueElements(content2, 2);

        this.assert(
            elements1.includes('花'),
            '应该提取到"花"元素'
        );

        this.assert(
            elements1.includes('树'),
            '应该提取到"树"元素'
        );

        this.assert(
            elements1.includes('太阳'),
            '应该提取到"太阳"元素'
        );

        this.assert(
            elements2.includes('画笔'),
            '应该提取到"画笔"元素'
        );

        this.assert(
            elements1.includes('page1'),
            '应该包含页面标识符'
        );

        this.assert(
            elements2.includes('page2'),
            '应该包含页面标识符'
        );

        console.log('内容1提取的元素:', elements1);
        console.log('内容2提取的元素:', elements2);
        console.log('✅ 唯一元素提取测试通过');
    }

    /**
     * 测试提示词增强
     */
    async testPromptEnhancement() {
        console.log('\n🎨 测试提示词增强...');

        const pageInfo = {
            pageNumber: 3,
            content: '小熊波波和小兔子一起在花园里玩耍，他们看到了美丽的蝴蝶在花丛中飞舞。',
            sceneType: 'garden',
            characters: [
                { name: '小熊波波', emotion: 'happy', action: 'playing' },
                { name: '小兔子', emotion: 'excited', action: 'watching' }
            ],
            uniqueElements: ['花', '蝴蝶', '花园', 'page3'],
            pageSpecificContent: '小熊波波和小兔子一起在花园里玩耍'
        };

        const enhancedPrompt = enhancedIllustrationService.promptManager.generateEnhancedImagePrompt(pageInfo);

        this.assert(
            enhancedPrompt.prompt.includes('花'),
            '增强提示词应该包含特有元素"花"'
        );

        this.assert(
            enhancedPrompt.prompt.includes('蝴蝶'),
            '增强提示词应该包含特有元素"蝴蝶"'
        );

        this.assert(
            enhancedPrompt.prompt.includes('page 3'),
            '增强提示词应该包含页面标识符'
        );

        this.assert(
            enhancedPrompt.pageInfo.uniqueElements.length > 0,
            '应该记录页面特有元素'
        );

        console.log('增强后的提示词:', enhancedPrompt.prompt.substring(0, 200) + '...');
        console.log('✅ 提示词增强测试通过');
    }

    /**
     * 断言方法
     */
    assert(condition, message) {
        if (condition) {
            this.testResults.push({ status: 'PASS', message });
        } else {
            this.testResults.push({ status: 'FAIL', message });
            console.error(`❌ 断言失败: ${message}`);
        }
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n📊 测试结果汇总:');
        console.log('='.repeat(50));
        
        const passed = this.testResults.filter(r => r.status === 'PASS').length;
        const failed = this.testResults.filter(r => r.status === 'FAIL').length;
        
        console.log(`✅ 通过: ${passed}`);
        console.log(`❌ 失败: ${failed}`);
        console.log(`📈 成功率: ${((passed / this.testResults.length) * 100).toFixed(1)}%`);
        
        if (failed > 0) {
            console.log('\n失败的测试:');
            this.testResults
                .filter(r => r.status === 'FAIL')
                .forEach(r => console.log(`  ❌ ${r.message}`));
        }
        
        console.log('='.repeat(50));
    }
}

// 导出测试类
export default OptimizationTest;

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
    window.runOptimizationTest = async function() {
        const test = new OptimizationTest();
        await test.runAllTests();
    };
    
    console.log('🧪 优化测试已加载，在控制台运行 runOptimizationTest() 开始测试');
}
