// 国际化服务
export type Language = 'zh' | 'en';

export interface TranslationKeys {
  // 通用
  title: string;
  continue: string;
  submit: string;
  restart: string;
  loading: string;
  error: string;
  
  // 导航
  page: string;
  of: string;
  progress: string;
  
  // 语音相关
  readContent: string;
  reading: string;
  voiceInput: string;
  listening: string;
  
  // 交互相关
  interactive: string;
  completed: string;
  pending: string;
  yourAnswer: string;
  timeRemaining: string;
  seconds: string;
  pleaseAnswer: string;
  
  // 按钮文本
  continueReading: string;
  nextPage: string;
  completeReading: string;
  waitForReading: string;
  submitAnswer: string;
  regenerateImage: string;
  generating: string;
  
  // 设置
  settings: string;
  language: string;
  voiceSettings: string;
  selectVoice: string;
  
  // 消息
  thankYou: string;
  browserNotSupported: string;
  generatingImage: string;
  imageGenerationFailed: string;
  
  // 分析报告
  analysisReport: string;
  languageVocabulary: string;
  logicalThinking: string;
  socialAdaptation: string;
  emotionalRecognition: string;
  overallRecommendation: string;
  completedInteractions: string;
  totalInteractions: string;
  
  // 故事生成
  storyGenerator: string;
  generateNewStory: string;
  selectExistingStory: string;
  storyTheme: string;
  characterAge: string;
  generateStory: string;

  // StoryGenerator 专用翻译
  aiStoryGenerator: string;
  personalizedStoryDescription: string;
  selectStoryTheme: string;
  pleaseSelectTheme: string;
  openaiNotConfigured: string;
  openaiConfigInstructions: string;
  restartServerInstructions: string;
  selected: string;
  generatingStory: string;
  generatingImages: string;
  back: string;
  storyGenerationInProgress: string;
  aiCreatingStory: string;
  imageGenerationProgress: string;
  pageImageCompleted: string;
  pageImageFailed: string;
  features: string;
  autismFriendlyContent: string;
  fullStoryPages: string;
  simpleLanguage: string;
  clearEmotionalExpression: string;
  guidedInteraction: string;
  autoGeneratedImages: string;
  configurationNotes: string;
  secureApiKeys: string;
  noSensitiveInfo: string;
  environmentSupport: string;

  // StorySelector 专用翻译
  storyLibrary: string;
  selectStoryDescription: string;
  storyLibraryStats: string;
  totalStories: string;
  generatedStories: string;
  defaultStories: string;
  lastGenerated: string;
  selectStory: string;
  noStoriesAvailable: string;
  pleaseGenerateNewStory: string;
  defaultStory: string;
  generatedStory: string;
  pages: string;
  interactions: string;
  selectThisStory: string;

  // 主题翻译
  themeInterpersonalRelations: string;
  themeFamilyLife: string;
  themeLegalKnowledge: string;
  themeMoralEthics: string;

  // 主题描述翻译
  themeDescInterpersonalRelations: string;
  themeDescFamilyLife: string;
  themeDescLegalKnowledge: string;
  themeDescMoralEthics: string;

  // 返回主页面
  returnHome: string;
  confirmReturnHome: string;
  returnHomeDescription: string;
}

class I18nService {
  private currentLanguage: Language = 'zh';
  private translations: Record<Language, TranslationKeys>;
  private listeners: ((language: Language) => void)[] = [];

  constructor() {
    this.translations = {
      zh: {
        // 通用
        title: '小熊波波的友谊冒险',
        continue: '继续',
        submit: '提交',
        restart: '重新开始',
        loading: '加载中...',
        error: '错误',
        
        // 导航
        page: '第',
        of: '页，共',
        progress: '进度',
        
        // 语音相关
        readContent: '🔊 朗读内容',
        reading: '🔊 正在朗读...',
        voiceInput: '🎤 语音输入',
        listening: '正在聆听...',
        
        // 交互相关
        interactive: '🎯 交互环节',
        completed: '✅ 已完成',
        pending: '⏳ 待回答',
        yourAnswer: '你的回答:',
        timeRemaining: '⏰ 请在30秒内回答问题',
        seconds: '秒',
        pleaseAnswer: '在这里输入你的回答...',
        
        // 按钮文本
        continueReading: '📖 继续阅读',
        nextPage: '📄 下一页',
        completeReading: '🎉 完成阅读',
        waitForReading: '⏳ 请等待朗读完成...',
        submitAnswer: '✅ 提交回答',
        regenerateImage: '🔄 重新生成插画',
        generating: '🎨 生成中...',
        
        // 设置
        settings: '设置',
        language: '语言',
        voiceSettings: '语音设置',
        selectVoice: '选择语音',
        
        // 消息
        thankYou: '谢谢你的回答！',
        browserNotSupported: '您的浏览器不支持语音识别功能',
        generatingImage: '🎨 正在生成个性化插画...',
        imageGenerationFailed: '生成插画失败',
        
        // 分析报告
        analysisReport: '分析报告',
        languageVocabulary: '语言词汇',
        logicalThinking: '逻辑思维',
        socialAdaptation: '社会适应',
        emotionalRecognition: '情感识别',
        overallRecommendation: '总体建议',
        completedInteractions: '完成交互',
        totalInteractions: '总交互数',
        
        // 故事生成
        storyGenerator: '故事生成器',
        generateNewStory: '生成新故事',
        selectExistingStory: '选择已有故事',
        storyTheme: '故事主题',
        characterAge: '角色年龄',
        generateStory: '生成故事',

        // StoryGenerator 专用翻译
        aiStoryGenerator: 'AI故事生成器',
        personalizedStoryDescription: '为自闭症儿童生成个性化的教育绘本故事',
        selectStoryTheme: '选择故事主题',
        pleaseSelectTheme: '请先选择一个故事主题',
        openaiNotConfigured: 'OpenAI API密钥未配置，请在.env文件中设置VITE_OPENAI_API_KEY',
        openaiConfigInstructions: 'OpenAI API密钥未配置。请在项目根目录的.env文件中设置：',
        restartServerInstructions: '设置完成后请重启开发服务器。',
        selected: '已选择',
        generatingStory: '生成故事中...',
        generatingImages: '生成插图中...',
        back: '返回',
        storyGenerationInProgress: '正在生成故事...',
        aiCreatingStory: 'AI正在为您创作一个关于"{theme}"的精彩故事，请稍候',
        imageGenerationProgress: '插图生成进度',
        pageImageCompleted: '第{page}页插图生成完成',
        pageImageFailed: '第{page}页插图生成失败',
        features: '功能特点',
        autismFriendlyContent: '针对自闭症儿童特点设计的故事内容',
        fullStoryPages: '12页完整故事，包含3个交互环节',
        simpleLanguage: '简单清晰的语言，避免复杂修辞',
        clearEmotionalExpression: '明确的情感表达和社交互动场景',
        guidedInteraction: '配套的引导提示帮助儿童参与互动',
        autoGeneratedImages: '自动生成配套插图，视觉效果更佳',
        configurationNotes: '配置说明',
        secureApiKeys: 'API密钥通过环境变量配置，更加安全',
        noSensitiveInfo: '无需在界面中输入敏感信息',
        environmentSupport: '支持开发和生产环境的不同配置',

        // StorySelector 专用翻译
        storyLibrary: '故事库',
        selectStoryDescription: '选择一个故事开始阅读，或生成新的故事',
        storyLibraryStats: '故事库统计',
        totalStories: '总故事数',
        generatedStories: '生成故事',
        defaultStories: '默认故事',
        lastGenerated: '最近生成',
        selectStory: '选择故事',
        noStoriesAvailable: '暂无可用故事，请生成新故事。',
        pleaseGenerateNewStory: '请生成新故事',
        defaultStory: '默认故事',
        generatedStory: '生成故事',
        pages: '页',
        interactions: '个交互',
        selectThisStory: '选择此故事',

        // 主题翻译
        themeInterpersonalRelations: '人际关系',
        themeFamilyLife: '家庭生活',
        themeLegalKnowledge: '法律常识',
        themeMoralEthics: '人伦道德',

        // 主题描述翻译
        themeDescInterpersonalRelations: '学习如何与他人建立友谊，培养社交技能和沟通能力',
        themeDescFamilyLife: '了解家庭的温暖，学习家庭责任和表达对家人的爱',
        themeDescLegalKnowledge: '用简单的方式学习基本规则，培养安全意识和守法观念',
        themeDescMoralEthics: '培养诚实、善良等品德，学习关爱他人和做正确的选择',

        // 返回主页面
        returnHome: '返回主页',
        confirmReturnHome: '确定要返回主页吗？当前的阅读进度将会保存。',
        returnHomeDescription: '返回到故事选择页面'
      },
      en: {
        // 通用
        title: 'Bobo Bear\'s Friendship Adventure',
        continue: 'Continue',
        submit: 'Submit',
        restart: 'Restart',
        loading: 'Loading...',
        error: 'Error',
        
        // 导航
        page: 'Page',
        of: 'of',
        progress: 'Progress',
        
        // 语音相关
        readContent: '🔊 Read Content',
        reading: '🔊 Reading...',
        voiceInput: '🎤 Voice Input',
        listening: 'Listening...',
        
        // 交互相关
        interactive: '🎯 Interactive',
        completed: '✅ Completed',
        pending: '⏳ Pending',
        yourAnswer: 'Your Answer:',
        timeRemaining: '⏰ Please answer within 30 seconds',
        seconds: 'seconds',
        pleaseAnswer: 'Enter your answer here...',
        
        // 按钮文本
        continueReading: '📖 Continue Reading',
        nextPage: '📄 Next Page',
        completeReading: '🎉 Complete Reading',
        waitForReading: '⏳ Please wait for reading to complete...',
        submitAnswer: '✅ Submit Answer',
        regenerateImage: '🔄 Regenerate Image',
        generating: '🎨 Generating...',
        
        // 设置
        settings: 'Settings',
        language: 'Language',
        voiceSettings: 'Voice Settings',
        selectVoice: 'Select Voice',
        
        // 消息
        thankYou: 'Thank you for your answer!',
        browserNotSupported: 'Your browser does not support speech recognition',
        generatingImage: '🎨 Generating personalized illustration...',
        imageGenerationFailed: 'Failed to generate illustration',
        
        // 分析报告
        analysisReport: 'Analysis Report',
        languageVocabulary: 'Language Vocabulary',
        logicalThinking: 'Logical Thinking',
        socialAdaptation: 'Social Adaptation',
        emotionalRecognition: 'Emotional Recognition',
        overallRecommendation: 'Overall Recommendation',
        completedInteractions: 'Completed Interactions',
        totalInteractions: 'Total Interactions',
        
        // 故事生成
        storyGenerator: 'Story Generator',
        generateNewStory: 'Generate New Story',
        selectExistingStory: 'Select Existing Story',
        storyTheme: 'Story Theme',
        characterAge: 'Character Age',
        generateStory: 'Generate Story',

        // StoryGenerator 专用翻译
        aiStoryGenerator: 'AI Story Generator',
        personalizedStoryDescription: 'Generate personalized educational storybooks for children with autism spectrum disorder',
        selectStoryTheme: 'Select Story Theme',
        pleaseSelectTheme: 'Please select a story theme first',
        openaiNotConfigured: 'OpenAI API key not configured. Please set VITE_OPENAI_API_KEY in .env file',
        openaiConfigInstructions: 'OpenAI API key not configured. Please set in the .env file in the project root:',
        restartServerInstructions: 'Please restart the development server after configuration.',
        selected: 'Selected',
        generatingStory: 'Generating Story...',
        generatingImages: 'Generating Images...',
        back: 'Back',
        storyGenerationInProgress: 'Generating story...',
        aiCreatingStory: 'AI is creating an exciting story about "{theme}" for you, please wait',
        imageGenerationProgress: 'Image Generation Progress',
        pageImageCompleted: 'Page {page} image generation completed',
        pageImageFailed: 'Page {page} image generation failed',
        features: 'Features',
        autismFriendlyContent: 'Story content designed for children with autism spectrum characteristics',
        fullStoryPages: '12 complete story pages with 3 interactive sessions',
        simpleLanguage: 'Simple and clear language, avoiding complex rhetoric',
        clearEmotionalExpression: 'Clear emotional expression and social interaction scenarios',
        guidedInteraction: 'Supporting guidance prompts to help children participate in interactions',
        autoGeneratedImages: 'Automatically generated supporting illustrations for better visual effects',
        configurationNotes: 'Configuration Notes',
        secureApiKeys: 'API keys configured through environment variables for better security',
        noSensitiveInfo: 'No need to enter sensitive information in the interface',
        environmentSupport: 'Support for different configurations in development and production environments',

        // StorySelector 专用翻译
        storyLibrary: 'Story Library',
        selectStoryDescription: 'Select a story to start reading, or generate a new story',
        storyLibraryStats: 'Story Library Statistics',
        totalStories: 'Total Stories',
        generatedStories: 'Generated Stories',
        defaultStories: 'Default Stories',
        lastGenerated: 'Last Generated',
        selectStory: 'Select Story',
        noStoriesAvailable: 'No stories available, please generate a new story.',
        pleaseGenerateNewStory: 'Please generate a new story',
        defaultStory: 'Default Story',
        generatedStory: 'Generated Story',
        pages: 'pages',
        interactions: 'interactions',
        selectThisStory: 'Select This Story',

        // 主题翻译
        themeInterpersonalRelations: 'Interpersonal Relations',
        themeFamilyLife: 'Family Life',
        themeLegalKnowledge: 'Legal Knowledge',
        themeMoralEthics: 'Moral Ethics',

        // 主题描述翻译
        themeDescInterpersonalRelations: 'Learn how to build friendships with others and develop social skills and communication abilities',
        themeDescFamilyLife: 'Understand the warmth of family, learn family responsibilities and express love for family members',
        themeDescLegalKnowledge: 'Learn basic rules in simple ways, develop safety awareness and law-abiding concepts',
        themeDescMoralEthics: 'Cultivate honesty, kindness and other virtues, learn to care for others and make right choices',

        // 返回主页面
        returnHome: 'Return Home',
        confirmReturnHome: 'Are you sure you want to return to the home page? Your current reading progress will be saved.',
        returnHomeDescription: 'Return to story selection page'
      }
    };

    // 从localStorage读取保存的语言设置
    const savedLanguage = localStorage.getItem('storybook-language') as Language;
    if (savedLanguage && (savedLanguage === 'zh' || savedLanguage === 'en')) {
      this.currentLanguage = savedLanguage;
    }
  }

  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  setLanguage(language: Language): void {
    this.currentLanguage = language;
    localStorage.setItem('storybook-language', language);
    this.notifyListeners();
  }

  t(key: keyof TranslationKeys): string {
    return this.translations[this.currentLanguage][key];
  }

  addLanguageChangeListener(listener: (language: Language) => void): void {
    this.listeners.push(listener);
  }

  removeLanguageChangeListener(listener: (language: Language) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentLanguage));
  }
}

export const i18nService = new I18nService();
export default i18nService;
