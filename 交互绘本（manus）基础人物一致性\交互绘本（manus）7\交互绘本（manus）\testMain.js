// 测试OpenAI集成的主脚本
// 用于验证故事和图片生成功能

import React from 'react';
import ReactDOM from 'react-dom';
import openAIService from './openAIService';
import { runAllTests } from './openAIServiceTest';

// 初始化API密钥
const API_KEY = '********************************************************************************************************************************************************************';

// 主测试函数
async function main() {
  console.log('开始测试OpenAI集成...');
  
  try {
    // 运行所有测试
    const results = await runAllTests();
    
    // 输出测试结果
    console.log('测试结果:', JSON.stringify(results, null, 2));
    
    // 显示成功消息
    console.log('所有测试通过！OpenAI集成功能正常工作。');
    
    return {
      success: true,
      results
    };
  } catch (error) {
    console.error('测试失败:', error);
    
    return {
      success: false,
      error: error.message
    };
  }
}

// 导出主函数
export default main;
