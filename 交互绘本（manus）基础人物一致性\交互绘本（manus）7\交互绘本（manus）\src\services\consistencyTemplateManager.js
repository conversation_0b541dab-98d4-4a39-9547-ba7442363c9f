/**
 * 绘本插画一致性关键词模板管理器
 * 基于专业的一致性模板，确保所有插画风格统一
 */

class ConsistencyTemplateManager {
    constructor() {
        // 基础风格固定词组 - 每张图都必须包含
        this.coreStyleKeywords = "children's book illustration, soft watercolor style, warm pastel colors, gentle lighting, simple clean lines, minimalist background, picture book art, storybook illustration, hand-drawn aesthetic";
        
        // 固定艺术风格描述
        this.artStyleKeywords = "watercolor painting, soft brush strokes, paper texture, children's book art, gentle shading, minimal details, clean composition";
        
        // 光照一致性
        this.lightingKeywords = "soft natural lighting, warm golden hour light, gentle shadows, diffused sunlight";
        
        // 视角保持
        this.perspectiveKeywords = "eye-level perspective, medium shot, centered composition, child-friendly viewpoint";
        
        // 质量参数
        this.qualityKeywords = "high quality, masterpiece, detailed illustration, professional artwork";
    }

    /**
     * 根据故事角色生成固定人物描述模板
     * @param {Array} storyCharacters - 故事角色列表
     * @returns {Object} 角色描述模板
     */
    generateCharacterTemplates(storyCharacters) {
        const templates = {};
        
        storyCharacters.forEach((character, index) => {
            const charName = typeof character === 'string' ? character : character.name;
            const charDesc = typeof character === 'string' ? '' : character.description;
            
            // 根据角色类型生成固定模板
            if (charName.includes('小女孩') || charName.includes('女孩') || charName.includes('丽丽')) {
                templates[charName] = this.generateGirlTemplate(charDesc);
            } else if (charName.includes('小男孩') || charName.includes('男孩') || charName.includes('小明')) {
                templates[charName] = this.generateBoyTemplate(charDesc);
            } else if (charName.includes('兔') || charName.includes('rabbit')) {
                templates[charName] = this.generateRabbitTemplate(charDesc);
            } else if (charName.includes('熊') || charName.includes('bear')) {
                templates[charName] = this.generateBearTemplate(charDesc);
            } else if (charName.includes('猫') || charName.includes('cat')) {
                templates[charName] = this.generateCatTemplate(charDesc);
            } else {
                // 通用角色模板
                templates[charName] = this.generateGenericTemplate(charName, charDesc);
            }
        });
        
        console.log('🎭 生成的角色模板:', templates);
        return templates;
    }

    /**
     * 生成小女孩角色模板
     */
    generateGirlTemplate(description) {
        return "a young girl with brown curly hair in two ponytails, wearing a red cardigan and blue overalls, bright green eyes, short stature, friendly smile";
    }

    /**
     * 生成小男孩角色模板
     */
    generateBoyTemplate(description) {
        return "a young boy with short brown hair, wearing a blue t-shirt and khaki shorts, warm brown eyes, cheerful expression";
    }

    /**
     * 生成兔子角色模板
     */
    generateRabbitTemplate(description) {
        return "a small white rabbit with long ears, pink nose, wearing a yellow vest, bright blue eyes, fluffy tail";
    }

    /**
     * 生成熊角色模板
     */
    generateBearTemplate(description) {
        return "a friendly brown bear cub with round ears, wearing a red shirt, kind dark eyes, soft fur";
    }

    /**
     * 生成猫角色模板
     */
    generateCatTemplate(description) {
        return "a small orange tabby cat with white chest marking, green eyes, blue collar with bell";
    }

    /**
     * 生成通用角色模板
     */
    generateGenericTemplate(name, description) {
        if (description) {
            // 如果有描述，尝试转换为英文模板
            return this.convertToEnglishTemplate(description);
        }
        return `a friendly character named ${name}, warm expression, child-friendly appearance`;
    }

    /**
     * 将中文描述转换为英文模板
     */
    convertToEnglishTemplate(chineseDesc) {
        // 简单的转换逻辑，可以根据需要扩展
        let template = chineseDesc;
        
        // 颜色转换
        template = template.replace(/白色/g, 'white');
        template = template.replace(/棕色/g, 'brown');
        template = template.replace(/黑色/g, 'black');
        template = template.replace(/红色/g, 'red');
        template = template.replace(/蓝色/g, 'blue');
        template = template.replace(/黄色/g, 'yellow');
        template = template.replace(/绿色/g, 'green');
        
        // 服装转换
        template = template.replace(/围裙/g, 'apron');
        template = template.replace(/衣服/g, 'clothing');
        template = template.replace(/帽子/g, 'hat');
        
        return template;
    }

    /**
     * 生成情绪表达词汇
     * @param {string} mood - 情绪类型
     * @returns {string} 情绪描述
     */
    generateEmotionKeywords(mood) {
        const emotions = {
            'happy': 'bright smile, sparkling eyes, cheerful expression, uplifted posture, warm atmosphere',
            'curious': 'wide curious eyes, slightly tilted head, questioning expression, exploratory pose',
            'peaceful': 'gentle smile, peaceful expression, relaxed posture, calm atmosphere',
            'surprised': 'wide eyes, open mouth, surprised expression, animated gesture',
            'excited': 'bright eyes, big smile, energetic posture, joyful expression',
            'thoughtful': 'contemplative look, gentle expression, quiet pose, reflective mood'
        };
        
        return emotions[mood] || emotions['happy'];
    }

    /**
     * 生成场景描述
     * @param {string} sceneType - 场景类型
     * @returns {string} 场景描述
     */
    generateSceneKeywords(sceneType) {
        const scenes = {
            'home': 'cozy home interior with wooden furniture, morning sunlight through window, peaceful atmosphere',
            'garden': 'blooming flowers and green grass, blue sky with fluffy clouds, joyful atmosphere',
            'forest': 'tall trees and dappled sunlight, forest path with fallen leaves, natural atmosphere',
            'school': 'simple desks and colorful books, soft natural lighting, learning atmosphere',
            'park': 'green grass and playground equipment, sunny day with gentle breeze, playful atmosphere',
            'bedroom': 'soft bed with colorful pillows, warm lamp light, cozy atmosphere'
        };
        
        return scenes[sceneType] || scenes['garden'];
    }

    /**
     * 构建完整的一致性提示词
     * @param {Object} params - 参数对象
     * @returns {string} 完整提示词
     */
    buildConsistentPrompt(params) {
        const {
            characterTemplates = {},
            characters = [],
            actions = [],
            sceneType = 'garden',
            mood = 'happy',
            objects = [],
            pageNumber = 1
        } = params;

        let promptParts = [];

        // 1. 基础风格固定词组（必须包含）
        promptParts.push(this.coreStyleKeywords);

        // 2. 人物固定描述
        console.log(`🔍 处理角色列表:`, characters);
        console.log(`🔍 可用角色模板:`, Object.keys(characterTemplates));

        characters.forEach(character => {
            const charName = typeof character === 'string' ? character : character.name;
            console.log(`🔍 查找角色模板: ${charName}`);

            if (characterTemplates[charName]) {
                console.log(`✅ 找到角色模板: ${charName} -> ${characterTemplates[charName]}`);
                promptParts.push(characterTemplates[charName]);
            } else {
                console.log(`❌ 未找到角色模板: ${charName}`);
                // 尝试模糊匹配
                const fuzzyMatch = this.findFuzzyCharacterMatch(charName, characterTemplates);
                if (fuzzyMatch) {
                    console.log(`🔄 使用模糊匹配: ${charName} -> ${fuzzyMatch}`);
                    promptParts.push(characterTemplates[fuzzyMatch]);
                } else {
                    console.log(`⚠️ 无法匹配角色，使用通用描述`);
                    const genericTemplate = this.generateGenericTemplate(charName, '');
                    promptParts.push(genericTemplate);
                }
            }
        });

        // 3. 动作描述
        if (actions.length > 0) {
            promptParts.push(actions.join(', '));
        }

        // 4. 场景描述
        promptParts.push(this.generateSceneKeywords(sceneType));

        // 5. 情绪表达
        promptParts.push(this.generateEmotionKeywords(mood));

        // 6. 场景物品
        if (objects.length > 0) {
            promptParts.push(`with ${objects.join(', ')}`);
        }

        // 7. 光照一致性
        promptParts.push(this.lightingKeywords);

        // 8. 视角保持
        promptParts.push(this.perspectiveKeywords);

        // 9. 艺术风格
        promptParts.push(this.artStyleKeywords);

        // 10. 质量参数
        promptParts.push(this.qualityKeywords);

        // 11. 页面标识
        promptParts.push(`page ${pageNumber} unique composition`);

        const finalPrompt = promptParts.join(', ');
        console.log(`🎨 一致性模板生成的提示词: "${finalPrompt}"`);
        
        return finalPrompt;
    }

    /**
     * 模糊匹配角色名称
     * @param {string} charName - 角色名称
     * @param {Object} characterTemplates - 角色模板
     * @returns {string|null} 匹配的模板键名
     */
    findFuzzyCharacterMatch(charName, characterTemplates) {
        const templateKeys = Object.keys(characterTemplates);

        // 精确匹配
        if (templateKeys.includes(charName)) {
            return charName;
        }

        // 包含匹配
        for (const key of templateKeys) {
            if (charName.includes(key) || key.includes(charName)) {
                return key;
            }
        }

        // 类型匹配
        const typeMatches = {
            '兔': ['兔子', 'rabbit'],
            '熊': ['熊', 'bear'],
            '猫': ['猫', 'cat'],
            '女孩': ['女孩', 'girl'],
            '男孩': ['男孩', 'boy']
        };

        for (const [type, keywords] of Object.entries(typeMatches)) {
            if (keywords.some(keyword => charName.includes(keyword))) {
                const matchedKey = templateKeys.find(key =>
                    keywords.some(kw => key.includes(kw))
                );
                if (matchedKey) {
                    return matchedKey;
                }
            }
        }

        return null;
    }

    /**
     * 生成负面提示词（专门针对一致性）
     * @returns {string} 负面提示词
     */
    generateConsistentNegativePrompt() {
        return "inconsistent character design, different art styles, varying color palettes, realistic photography, 3D rendering, anime style, manga style, cartoon style, different lighting conditions, complex backgrounds, cluttered composition, adult themes, dark atmosphere, scary elements, violence, inappropriate content, text, watermarks, signatures, blurry, low quality, deformed, ugly, bad anatomy, extra limbs, malformed hands, poorly drawn, distorted proportions";
    }
}

export default ConsistencyTemplateManager;
