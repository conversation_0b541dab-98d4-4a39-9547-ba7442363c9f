// 简单检查环境变量配置
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

console.log('🔍 检查环境变量配置...\n');

// 检查OpenAI API密钥
const openaiKey = process.env.VITE_OPENAI_API_KEY;
console.log('VITE_OPENAI_API_KEY 状态:');
if (!openaiKey) {
  console.log('❌ 未找到');
} else if (openaiKey === 'your_openai_api_key_here') {
  console.log('⚠️ 仍为默认值，需要设置真实密钥');
} else {
  console.log('✅ 已配置');
  console.log('🔑 密钥前缀:', openaiKey.substring(0, 15) + '...');
  console.log('📏 密钥长度:', openaiKey.length);
}

console.log('\n其他环境变量:');
console.log('VITE_LIBLIB_ACCESS_KEY:', process.env.VITE_LIBLIB_ACCESS_KEY ? '✅ 已配置' : '❌ 未配置');
console.log('VITE_LIBLIB_SECRET_KEY:', process.env.VITE_LIBLIB_SECRET_KEY ? '✅ 已配置' : '❌ 未配置');

console.log('\n✅ 环境变量检查完成');
