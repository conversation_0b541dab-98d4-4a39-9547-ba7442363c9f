// 基于用户回答生成插画的核心逻辑实现
// 使用LIBLIB AI平台进行图片生成

import liblibService from './liblibService.js';
import { debugLiblibInBrowser } from './liblibServiceDebug.js';
import {
  IMAGE2IMAGE_PROMPT_TEMPLATE,
  INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE,
  buildStandardIllustrationKeywords,
  buildInteractiveIllustrationKeywords,
  generateDynamicCharacterKeywords,
  generateSmartKeywords,
  CHARACTER_TEMPLATE_STRUCTURE,
  DEFAULT_CHARACTER_TEMPLATES,
  BASE_STYLE_KEYWORDS,
  EMOTION_KEYWORDS
} from './promptTemplates.js';
import enhancedIllustrationService from './enhancedIllustrationService.js';

// 增强的风格描述 - 专为自闭症儿童设计
const STYLE_DESCRIPTION = `
温暖友好的儿童插画，专为自闭症儿童设计，使用柔和的色彩和简单清晰的形状，角色表情丰富且易于理解。
水彩画风格，轮廓线条清晰且一致，色彩饱和度适中。主要使用温暖的棕色、绿色、蓝色和黄色，避免过于刺激的鲜艳色彩。
柔和的光影效果，避免强烈对比和复杂阴影，确保视觉舒适。轻微的水彩纹理，保持整体平滑感。
场景设计简洁不复杂，背景元素适量且有序排列，主体突出，避免过多分散注意力的细节。
保持与故事其他插图的一致风格和色调，确保整体视觉连贯性。
`;

/**
 * 从故事数据中获取角色描述
 * @param {Object} storyData - 故事数据
 * @returns {Object} 角色描述映射
 */
function getCharacterDescriptions(storyData) {
  const characterMap = {};

  if (storyData && storyData.characters) {
    storyData.characters.forEach(char => {
      characterMap[char.name] = char.description;
    });
  }

  // 默认角色描述（回退选项）
  const defaultDescriptions = {
    "小熊波波": "一只可爱的小棕熊，有着温暖的棕色毛发，圆圆的脸庞，大大的黑色眼睛，红色上衣，蓝色短裤",
    "小兔莉莉": "一只优雅的小白兔，有着雪白柔软的毛发，粉红色的鼻子，长长的耳朵，粉色连衣裙",
    "猫头鹰老师": "一只聪明的猫头鹰，有着棕色和白色相间的羽毛，大大的圆眼睛，戴着圆形眼镜",
    "松鼠兄弟": "两只活泼的小松鼠，有着红棕色的毛发和蓬松的尾巴，一个穿黄色衣服，一个穿绿色衣服",
    "乌龟爷爷": "一只慢吞吞的老乌龟，有着深绿色的壳和温和的表情，眼神慈祥"
  };

  // 合并默认描述和故事数据中的描述
  return { ...defaultDescriptions, ...characterMap };
}

// 情感表达指南 - 帮助生成更适合自闭症儿童理解的情感表达
const EMOTION_EXPRESSION_GUIDE = `
情感表达应清晰且易于识别：
- 开心/高兴：明显的微笑，眼睛弯曲成弧形
- 悲伤：嘴角下垂，眉毛向上倾斜
- 惊讶：圆形嘴巴，睁大的眼睛
- 害怕：缩小的身体姿势，略微后倾
- 友好：微笑并伸出手/爪子
- 好奇：略微倾斜的头部，专注的眼神

避免复杂或模糊的情感表达，确保情感状态一目了然。
`;

// 交互场景指南 - 针对交互页面的特殊处理
const INTERACTIVE_SCENE_GUIDE = `
交互场景应展现开放性和包容性：
- 角色之间保持适当距离，展示积极互动
- 场景应有邀请性，预留空间让想象中的新角色或元素加入
- 可以包含与用户回答相关的新元素，但保持与故事世界观的一致性
- 避免过于拥挤或混乱的场景，保持视觉清晰度
`;

/**
 * 从用户回答中提取关键内容
 * @param {string} answer - 用户的回答内容
 * @returns {Object} 提取的关键内容
 */
function extractKeyContent(answer) {
  // 提取角色 - 扩展了更多可能的角色名称变体
  const characterPatterns = [
    { names: ['波波', '小熊波波', '小熊'], character: '小熊波波' },
    { names: ['莉莉', '小兔莉莉', '小兔'], character: '小兔莉莉' },
    { names: ['乌龟', '乌龟老师', '老师'], character: '乌龟老师' },
    { names: ['松鼠', '松鼠兄弟'], character: '松鼠兄弟' }
  ];

  const characters = [];
  characterPatterns.forEach(pattern => {
    if (pattern.names.some(name => answer.includes(name))) {
      characters.push(pattern.character);
    }
  });

  // 提取动作和场景 - 扩展了更多关键词
  const actions = [];
  const actionKeywords = [
    '打招呼', '分享', '帮助', '玩', '说话', '微笑', '拥抱', '交朋友',
    '给予', '接受', '学习', '教导', '聆听', '阅读', '画画', '唱歌',
    '跳舞', '跑步', '散步', '探索', '发现', '思考', '想象'
  ];
  actionKeywords.forEach(action => {
    if (answer.includes(action)) actions.push(action);
  });

  // 提取情感 - 扩展了更多情感词汇
  const emotions = [];
  const emotionKeywords = [
    '开心', '高兴', '害怕', '紧张', '兴奋', '好奇', '担心', '勇敢', '友好',
    '快乐', '满足', '惊讶', '惊喜', '感激', '温暖', '安心', '自信', '骄傲',
    '期待', '专注', '平静', '满意'
  ];
  emotionKeywords.forEach(emotion => {
    if (answer.includes(emotion)) emotions.push(emotion);
  });

  // 提取场景元素 - 扩展了更多场景元素
  const sceneElements = [];
  const sceneKeywords = [
    '森林', '树', '花', '草地', '河流', '木屋', '阳光', '雨', '野餐',
    '学校', '教室', '操场', '图书馆', '桥', '山', '洞穴', '池塘', '瀑布',
    '彩虹', '云', '星星', '月亮', '沙滩', '海', '船', '帐篷', '篝火'
  ];
  sceneKeywords.forEach(element => {
    if (answer.includes(element)) sceneElements.push(element);
  });

  // 提取物品 - 新增物品提取
  const items = [];
  const itemKeywords = [
    '书', '玩具', '食物', '水果', '蛋糕', '礼物', '气球', '画', '信',
    '背包', '帽子', '伞', '望远镜', '地图', '笔', '纸', '乐器', '球',
    '风筝', '船', '车', '自行车', '灯笼', '花束', '相机'
  ];
  itemKeywords.forEach(item => {
    if (answer.includes(item)) items.push(item);
  });

  // 提取时间和天气 - 新增时间和天气提取
  const timeAndWeather = [];
  const timeWeatherKeywords = [
    '早晨', '中午', '下午', '傍晚', '晚上', '夜晚',
    '晴天', '雨天', '多云', '雾', '雪', '风', '彩虹'
  ];
  timeWeatherKeywords.forEach(tw => {
    if (answer.includes(tw)) timeAndWeather.push(tw);
  });

  // 如果没有提取到足够的内容，提供默认值
  return {
    characters: characters.length > 0 ? characters : ['小熊波波', '小兔莉莉'],
    actions: actions.length > 0 ? actions : ['微笑', '交朋友'],
    emotions: emotions.length > 0 ? emotions : ['友好', '开心'],
    sceneElements: sceneElements.length > 0 ? sceneElements : ['森林', '阳光'],
    items: items.length > 0 ? items : [],
    timeAndWeather: timeAndWeather.length > 0 ? timeAndWeather : ['白天']
  };
}

/**
 * 增强版：构建图像生成提示词（使用优化的关键词结构）
 * @param {string} answer - 用户的回答内容
 * @param {Object} context - 当前故事上下文
 * @param {number} pageId - 页面ID
 * @param {Object} storyData - 完整的故事数据
 * @returns {string} 完整的关键词提示
 */
function buildImagePrompt(answer, context, pageId, storyData = null) {
  console.log(`🎨 构建第${pageId}页插画关键词（优化版）...`);
  console.log(`📝 用户回答: ${answer}`);
  console.log(`📚 故事数据:`, storyData);

  // 尝试使用OpenAI生成的image_keywords（如果存在）
  if (storyData && storyData.pages) {
    const currentPage = storyData.pages.find(p => p.id === pageId);
    if (currentPage && currentPage.image_keywords) {
      console.log(`✅ 使用OpenAI生成的图像关键词`);
      return buildPromptFromKeywords(currentPage.image_keywords, answer, pageId, storyData);
    }
  }

  console.log(`🔄 使用新的动态关键词生成系统`);

  // 获取主要角色数据
  let mainCharacterData = null;
  if (storyData && storyData.characters && storyData.characters.length > 0) {
    mainCharacterData = storyData.characters[0]; // 使用第一个角色作为主角
    console.log('🎭 使用OpenAI角色数据:', mainCharacterData);
  }

  // 确定场景
  let scene = 'in a beautiful garden with flowers and trees';
  if (context && context.currentPage) {
    const pageContent = context.currentPage.content || context.currentPage.question || '';
    if (pageContent.includes('公园') || pageContent.includes('park')) {
      scene = 'in a sunny park with playground equipment';
    } else if (pageContent.includes('学校') || pageContent.includes('school')) {
      scene = 'in a bright classroom with books and desks';
    } else if (pageContent.includes('家') || pageContent.includes('home')) {
      scene = 'in a cozy home interior with warm lighting';
    } else if (pageContent.includes('森林') || pageContent.includes('forest')) {
      scene = 'in a magical forest with tall trees and sunlight';
    }
  }

  // 确定情绪
  let emotion = 'happy';
  if (answer.includes('好奇') || answer.includes('想知道')) {
    emotion = 'curious';
  } else if (answer.includes('安静') || answer.includes('平静')) {
    emotion = 'peaceful';
  } else if (answer.includes('惊讶') || answer.includes('意外')) {
    emotion = 'surprised';
  }

  // 使用新的智能关键词生成系统（交互版）
  const keywords = generateSmartKeywords(
    answer,
    mainCharacterData,
    scene
  );

  console.log('🎯 生成的交互智能关键词:', keywords);
  return keywords;
}

/**
 * 基于OpenAI关键词构建提示词（优化版）
 * @param {Object} keywords - OpenAI生成的图像关键词
 * @param {string} answer - 用户回答
 * @param {number} pageId - 页面ID
 * @param {Object} storyData - 故事数据
 * @returns {string} 构建的关键词提示
 */
function buildPromptFromKeywords(keywords, answer, pageId, storyData) {
  console.log(`🔑 使用OpenAI关键词构建优化提示词`);

  // 获取主要角色数据
  let mainCharacterData = null;
  if (storyData && storyData.characters && storyData.characters.length > 0) {
    mainCharacterData = storyData.characters[0];
  }

  // 确定场景
  let scene = keywords.scene || 'in a beautiful garden with flowers and trees';

  // 确定情绪
  let emotion = 'happy';
  if (keywords.mood) {
    if (keywords.mood.includes('curious') || keywords.mood.includes('好奇')) {
      emotion = 'curious';
    } else if (keywords.mood.includes('peaceful') || keywords.mood.includes('平静')) {
      emotion = 'peaceful';
    } else if (keywords.mood.includes('surprised') || keywords.mood.includes('惊讶')) {
      emotion = 'surprised';
    }
  }

  // 确定动作
  let action = extractActionFromAnswer(answer);
  if (keywords.actions && keywords.actions.length > 0) {
    // 将中文动作转换为英文
    const chineseActions = keywords.actions.join('，');
    if (chineseActions.includes('玩')) action = 'playing happily';
    else if (chineseActions.includes('说话')) action = 'talking cheerfully';
    else if (chineseActions.includes('微笑')) action = 'smiling brightly';
    else if (chineseActions.includes('跑')) action = 'running joyfully';
  }

  // 使用新的智能关键词生成系统（基于OpenAI关键词）
  const optimizedKeywords = generateSmartKeywords(
    answer,
    mainCharacterData,
    scene
  );

  console.log('🎯 基于OpenAI关键词生成的智能提示词:', optimizedKeywords);
  return optimizedKeywords;
}

/**
 * 从用户回答中提取具体事件
 * @param {string} answer - 用户回答
 * @returns {Array} 事件列表
 */
function extractUserEvents(answer) {
  const events = [];

  // 动作词汇
  const actionPatterns = [
    /说["""]([^"""]+)["""]/g,  // 提取对话
    /打招呼/g, /问好/g, /介绍/g,
    /拥抱/g, /握手/g, /微笑/g,
    /分享/g, /给/g, /帮助/g,
    /一起玩/g, /一起做/g, /合作/g,
    /道歉/g, /原谅/g, /安慰/g
  ];

  actionPatterns.forEach(pattern => {
    const matches = answer.match(pattern);
    if (matches) {
      matches.forEach(match => {
        if (!events.includes(match)) {
          events.push(match);
        }
      });
    }
  });

  // 如果没有找到具体动作，尝试提取整体描述
  if (events.length === 0) {
    if (answer.includes('会') || answer.includes('要')) {
      events.push(answer);
    }
  }

  return events;
}

/**
 * 识别用户回答中提到的角色
 * @param {string} answer - 用户回答
 * @param {Object} characterDescriptions - 角色描述映射
 * @returns {Array} 角色信息列表
 */
function identifyMentionedCharacters(answer, characterDescriptions) {
  const mentionedChars = [];

  Object.keys(characterDescriptions).forEach(charName => {
    if (answer.includes(charName) || answer.includes(charName.replace('小', ''))) {
      mentionedChars.push({
        name: charName,
        description: characterDescriptions[charName]
      });
    }
  });

  // 如果没有明确提到角色，根据上下文推断
  if (mentionedChars.length === 0) {
    // 默认包含主角
    if (characterDescriptions['小熊波波']) {
      mentionedChars.push({
        name: '小熊波波',
        description: characterDescriptions['小熊波波']
      });
    }
  }

  return mentionedChars;
}

/**
 * 增强版：获取最佳参考图像URL（优化版）
 * @param {number} currentPageIndex - 当前页面索引
 * @param {Array} allImages - 所有可用的图像URL
 * @returns {Array} 参考图像URL数组（只返回一张最佳图像）
 */
function getReferenceImages(currentPageIndex, allImages) {
  console.log(`🔍 为页面索引${currentPageIndex}选择最佳参考图像...`);
  console.log('📚 可用图像列表:', allImages.map(img => `页面${img.pageIndex}: ${img.url.substring(0, 50)}...`));

  // 只选择一张最相关的参考图像
  let bestReference = null;

  // 1. 优先选择前一页的图像（最相关）
  const prevImages = allImages.filter(img => img.pageIndex < currentPageIndex)
    .sort((a, b) => b.pageIndex - a.pageIndex);

  if (prevImages.length > 0) {
    bestReference = prevImages[0].url;
    console.log(`✅ 选择前一页图像作为最佳参考: 页面${prevImages[0].pageIndex}`);
  }
  // 2. 如果没有前一页图像，选择最近的任何图像
  else if (allImages.length > 0) {
    const sortedImages = [...allImages]
      .filter(img => img.pageIndex < currentPageIndex)
      .sort((a, b) => b.pageIndex - a.pageIndex);

    if (sortedImages.length > 0) {
      bestReference = sortedImages[0].url;
      console.log(`✅ 选择最近的图像作为参考: 页面${sortedImages[0].pageIndex}`);
    }
    // 3. 如果没有之前的图像，选择任何可用图像
    else if (allImages.length > 0) {
      bestReference = allImages[0].url;
      console.log(`✅ 选择第一个可用图像作为参考: 页面${allImages[0].pageIndex}`);
    }
  }

  // 返回单个最佳参考图像（如果有的话）
  const result = bestReference ? [bestReference] : [];

  console.log(`🎯 最终选择的参考图像数量: ${result.length}`);
  if (result.length > 0) {
    console.log('📋 最佳参考图像:', result[0].substring(0, 50) + '...');
  } else {
    console.log('⚠️ 没有找到合适的参考图像');
  }

  return result;
}

/**
 * 调用LIBLIB API生成图像 - 使用image2image功能保持风格一致性
 * @param {string} prompt - 图像生成提示词
 * @param {Array} referenceImages - 参考图像URL数组（用于image2image）
 * @param {string} userAnswer - 用户回答
 * @param {string} sceneDescription - 场景描述
 * @param {Object} storyData - 故事数据（可选）
 * @returns {Promise<string>} 生成的图像URL
 */
async function generateImage(prompt, referenceImages = [], userAnswer = '', sceneDescription = '', storyData = null) {
  try {
    console.log('使用LIBLIB平台image2image功能生成图像...');
    console.log('生成图像的提示词:', prompt);
    console.log('参考图像:', referenceImages);
    console.log('用户回答:', userAnswer);

    // 如果有参考图像，使用image2image功能
    if (referenceImages && referenceImages.length > 0) {
      // 选择最佳的参考图像（通常是最近的页面）
      const primaryReferenceImage = referenceImages[0];

      console.log('使用image2image功能，参考图像:', primaryReferenceImage);

      // 为image2image优化提示词
      const optimizedPrompt = optimizePromptForImage2Image(prompt, userAnswer, sceneDescription);

      console.log('优化后的image2image提示词:', optimizedPrompt);

      // 使用LIBLIB的image2image功能
      return await liblibService.generateImageFromImage(
        primaryReferenceImage,
        optimizedPrompt,
        '6-8岁'
      );
    } else {
      // 如果没有参考图像，使用交互式插画模板
      console.log('没有参考图像，使用交互式插画模板');

      // 构建交互式插画提示词（传入故事数据）
      const interactivePrompt = buildInteractiveIllustrationPrompt(
        userAnswer,
        sceneDescription || prompt,
        1,
        storyData // 使用传入的故事数据
      );

      console.log('交互式插画提示词:', interactivePrompt);

      return await liblibService.generateImage(interactivePrompt, '6-8岁');
    }
  } catch (error) {
    console.error('LIBLIB图像生成失败:', error);

    // 检查是否是敏感内容错误
    if (error.message && error.message.includes('敏感内容')) {
      console.warn('⚠️ 检测到敏感内容，尝试使用安全提示词重新生成...');

      try {
        // 使用安全的默认提示词重新生成
        const safePrompt = generateSafePrompt(userAnswer, sceneDescription);
        console.log('🔒 使用安全提示词:', safePrompt);

        // 重新尝试生成
        if (referenceImages && referenceImages.length > 0) {
          return await liblibService.generateImageFromImage(
            referenceImages[0],
            safePrompt,
            '6-8岁'
          );
        } else {
          return await liblibService.generateImage(safePrompt, '6-8岁');
        }
      } catch (retryError) {
        console.error('❌ 安全提示词重试也失败:', retryError);
        // 如果安全提示词也失败，返回默认图片或抛出错误
        throw new Error('图像生成失败：内容可能包含敏感信息，请尝试修改回答内容');
      }
    }

    // 如果是网络错误，提供详细的诊断信息
    if (error.message.includes('Failed to fetch')) {
      console.error('🌐 检测到网络请求失败，开始诊断...');

      try {
        // 运行浏览器环境诊断
        const diagnosis = await debugLiblibInBrowser();
        console.error('📊 诊断结果:', diagnosis);

        // 提供更友好的错误信息
        const friendlyError = new Error(
          `生成插画失败: 网络连接问题。${diagnosis.suggestions ? '建议: ' + diagnosis.suggestions.join(', ') : ''}`
        );
        friendlyError.originalError = error;
        friendlyError.diagnosis = diagnosis;
        throw friendlyError;
      } catch (diagError) {
        console.error('诊断过程失败:', diagError);
        // 如果诊断也失败了，返回原始错误
        throw error;
      }
    } else {
      throw error;
    }
  }
}

/**
 * 为image2image功能优化提示词
 * @param {string} originalPrompt - 原始提示词
 * @param {string} userAnswer - 用户回答
 * @param {string} sceneDescription - 场景描述
 * @returns {string} 优化后的提示词
 */
function optimizePromptForImage2Image(originalPrompt, userAnswer = '', sceneDescription = '') {
  // 使用专门的image2image模板
  const optimizedPrompt = IMAGE2IMAGE_PROMPT_TEMPLATE
    .replace('{scene_description}', sceneDescription || originalPrompt)
    .replace('{user_answer}', userAnswer)
    .replace('{age_range}', '6-8');

  return optimizedPrompt;
}

/**
 * 清理和过滤用户输入，避免敏感内容
 * @param {string} input - 用户输入
 * @returns {string} 清理后的输入
 */
function sanitizeUserInput(input) {
  if (!input || typeof input !== 'string') {
    return 'happy children playing together';
  }

  // 移除可能被误判的词汇，替换为更安全的表达
  let cleanInput = input
    .replace(/暴力|打架|战斗|攻击/g, '玩耍')
    .replace(/武器|刀|枪|剑/g, '玩具')
    .replace(/死|杀|伤害/g, '睡觉')
    .replace(/血|受伤/g, '红色')
    .replace(/恐怖|可怕|害怕/g, '有趣')
    .replace(/黑暗|阴暗/g, '安静')
    .replace(/哭|难过/g, '思考')
    .replace(/生气|愤怒/g, '认真');

  // 确保内容积极正面，限制长度
  if (cleanInput.length > 100) {
    cleanInput = cleanInput.substring(0, 100);
  }

  // 如果清理后内容为空或过短，使用默认内容
  if (cleanInput.length < 3) {
    cleanInput = 'happy children playing together in a beautiful garden';
  }

  return cleanInput;
}

/**
 * 生成安全的提示词，避免敏感内容检测
 * @param {string} userAnswer - 用户回答
 * @param {string} sceneDescription - 场景描述
 * @returns {string} 安全的提示词
 */
function generateSafePrompt(userAnswer, sceneDescription) {
  // 使用非常安全和积极的描述
  const safeElements = [
    'cute cartoon bear character',
    'children illustration style',
    'bright cheerful colors',
    'friendly atmosphere',
    'safe playground setting',
    'happy expression',
    'warm sunlight',
    'beautiful garden background',
    'colorful flowers',
    'blue sky with white clouds'
  ];

  // 构建安全提示词
  const safePrompt = `${safeElements.join(', ')}, wholesome children's book illustration, family-friendly content, educational theme, positive message, safe environment`;

  console.log('🔒 生成安全提示词:', safePrompt);
  return safePrompt;
}

/**
 * 构建交互式插画提示词（优化版 - 使用关键词结构）
 * @param {string} userAnswer - 用户回答
 * @param {string} storyContext - 故事上下文
 * @param {number} pageNumber - 页面编号
 * @param {Object} storyData - 故事数据（可选）
 * @returns {string} 交互式插画关键词
 */
function buildInteractiveIllustrationPrompt(userAnswer, storyContext, pageNumber, storyData = null) {
  console.log('🎨 构建交互插画关键词（优化版）...');
  console.log('用户回答:', userAnswer);
  console.log('故事上下文:', storyContext);
  console.log('故事数据:', storyData);

  // 清理用户输入以避免敏感内容检测
  const cleanUserAnswer = sanitizeUserInput(userAnswer);
  const cleanStoryContext = sanitizeUserInput(storyContext);

  // 获取主要角色数据
  let mainCharacterData = null;
  if (storyData && storyData.characters && storyData.characters.length > 0) {
    mainCharacterData = storyData.characters[0]; // 使用第一个角色作为主角
    console.log('使用OpenAI角色数据:', mainCharacterData);
  }

  // 确定场景
  let scene = 'in a beautiful garden with flowers and trees';
  if (cleanStoryContext.includes('公园') || cleanStoryContext.includes('park')) {
    scene = 'in a sunny park with playground equipment';
  } else if (cleanStoryContext.includes('学校') || cleanStoryContext.includes('school')) {
    scene = 'in a bright classroom with books and desks';
  } else if (cleanStoryContext.includes('家') || cleanStoryContext.includes('home')) {
    scene = 'in a cozy home interior with warm lighting';
  }

  // 确定情绪
  let emotion = 'happy';
  if (cleanUserAnswer.includes('好奇') || cleanUserAnswer.includes('想知道')) {
    emotion = 'curious';
  } else if (cleanUserAnswer.includes('安静') || cleanUserAnswer.includes('平静')) {
    emotion = 'peaceful';
  } else if (cleanUserAnswer.includes('惊讶') || cleanUserAnswer.includes('意外')) {
    emotion = 'surprised';
  }

  // 使用新的智能关键词生成系统
  const keywords = generateSmartKeywords(
    cleanUserAnswer,
    mainCharacterData,
    scene
  );

  console.log('🎯 生成的智能关键词:', keywords);
  return keywords;
}

// 注意：convertChineseCharacterToEnglishKeywords 函数已被 generateDynamicCharacterKeywords 替代
// 新函数在 promptTemplates.js 中，提供更完整和灵活的角色描述转换功能

/**
 * 生成基于用户回答的缓存键
 * @param {number} pageId - 页面ID
 * @param {string} answer - 用户回答
 * @returns {string} 缓存键
 */
function generateCacheKey(pageId, answer) {
  // 获取当前会话时间，用于区分不同的阅读会话
  const sessionTime = localStorage.getItem('last_session_time') || Date.now().toString();
  const sessionId = sessionTime.slice(-8); // 修复：使用slice而不是substring

  // 创建基于回答内容的哈希，确保相同回答使用相同缓存
  const answerHash = btoa(encodeURIComponent(answer)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);

  // 添加时间戳确保每次交互都生成新图片
  const timestamp = Date.now().toString().slice(-6);

  return `generated_image_page_${pageId}_${answerHash}_session_${sessionId}_${timestamp}`;
}

/**
 * 保存生成的图像到本地缓存
 * @param {string} imageUrl - 生成的图像URL
 * @param {number} pageId - 页面ID
 * @param {string} answer - 用户回答（用于个性化缓存）
 * @returns {Promise<string>} 本地图像路径
 */
async function saveGeneratedImage(imageUrl, pageId, answer) {
  try {
    // 在实际应用中，这里会实现将远程图像保存到本地的逻辑
    // 在前端应用中，可以使用localStorage或IndexedDB存储图像URL

    // 生成基于回答内容的缓存键
    const cacheKey = generateCacheKey(pageId, answer);

    // 存储图像URL到localStorage
    localStorage.setItem(cacheKey, imageUrl);

    // 同时存储缓存元数据
    const metadataKey = `${cacheKey}_metadata`;
    const metadata = {
      pageId,
      answer,
      imageUrl,
      timestamp: Date.now(),
      answerLength: answer.length
    };
    localStorage.setItem(metadataKey, JSON.stringify(metadata));

    console.log(`保存图像: ${imageUrl} 到页面ID: ${pageId}，回答哈希: ${cacheKey}`);

    // 返回图像URL作为本地路径
    return imageUrl;
  } catch (error) {
    console.error('保存图像失败:', error);
    throw error;
  }
}

/**
 * 从缓存中获取已生成的图像
 * @param {number} pageId - 页面ID
 * @param {string} answer - 用户回答
 * @returns {string|null} 缓存的图像URL或null
 */
function getCachedImage(pageId, answer) {
  // 暂时禁用缓存功能，确保每次都生成新的插画
  console.log('🚫 缓存功能已禁用，跳过缓存检查');
  return null;

  // 原缓存逻辑（已禁用）
  /*
  try {
    const cacheKey = generateCacheKey(pageId, answer);
    const cachedUrl = localStorage.getItem(cacheKey);

    if (cachedUrl) {
      console.log(`找到缓存图像: ${cachedUrl}，缓存键: ${cacheKey}`);

      // 更新访问时间
      const metadataKey = `${cacheKey}_metadata`;
      const metadata = JSON.parse(localStorage.getItem(metadataKey) || '{}');
      metadata.lastAccessed = Date.now();
      localStorage.setItem(metadataKey, JSON.stringify(metadata));
    }

    return cachedUrl;
  } catch (error) {
    console.error('获取缓存图像失败:', error);
    return null;
  }
  */
}

/**
 * 获取页面的所有缓存图像
 * @param {number} pageId - 页面ID
 * @returns {Array} 缓存图像列表
 */
function getPageCachedImages(pageId) {
  try {
    const keys = Object.keys(localStorage);
    const pageImageKeys = keys.filter(key =>
      key.startsWith(`generated_image_page_${pageId}_`) &&
      !key.endsWith('_metadata')
    );

    return pageImageKeys.map(key => {
      const imageUrl = localStorage.getItem(key);
      const metadataKey = `${key}_metadata`;
      const metadata = JSON.parse(localStorage.getItem(metadataKey) || '{}');

      return {
        cacheKey: key,
        imageUrl,
        metadata
      };
    }).sort((a, b) => (b.metadata.timestamp || 0) - (a.metadata.timestamp || 0));
  } catch (error) {
    console.error('获取页面缓存图像失败:', error);
    return [];
  }
}

/**
 * 主函数：根据用户回答生成插画（增强版）
 * @param {string} answer - 用户的回答内容
 * @param {number} pageId - 交互页面ID
 * @param {Object} context - 当前故事上下文
 * @param {Array} allImages - 所有可用的图像
 * @returns {Promise<string>} 生成的图像URL或路径
 */
export async function generateIllustrationFromAnswer(answer, pageId, context, allImages, storyData = null) {
  try {
    console.log(`🎨 使用增强版插画生成服务为页面${pageId}生成插画`);
    console.log(`📝 用户回答: "${answer}"`);

    // 清除该页面的所有旧缓存（防止意外使用）
    clearImageCache(pageId);

    // 获取故事数据（优先使用传入的storyData）
    const currentStoryData = storyData || context?.storyData;

    // 设置故事上下文到增强服务
    if (currentStoryData) {
      enhancedIllustrationService.setStoryContext(currentStoryData);
      console.log(`📚 已设置故事上下文，包含${currentStoryData.characters?.length || 0}个角色`);
    }

    // 提取关键内容用于角色识别
    const keyContent = extractKeyContent(answer);

    // 提取用户回答中的具体事件
    const userEvents = extractUserEvents(answer);
    console.log(`🎬 用户事件: ${userEvents.join(', ')}`);

    // 识别提到的角色
    const characterDescriptions = getCharacterDescriptions(currentStoryData);
    const mentionedCharacters = identifyMentionedCharacters(answer, characterDescriptions);
    console.log(`👥 识别的角色: ${mentionedCharacters.map(c => c.name).join(', ')}`);

    // 构建页面信息对象
    const pageInfo = {
      pageNumber: pageId,
      content: context?.currentPage?.content || '',
      sceneType: "interaction", // 交互页面
      characters: mentionedCharacters.map(char => ({
        name: char.name,
        description: char.description,
        emotion: keyContent.emotions.length > 0 ? keyContent.emotions[0] : "happy",
        action: keyContent.actions.length > 0 ? keyContent.actions[0] : "talking"
      })),
      isInteractive: true,
      userAnswer: answer,
      userEvents: userEvents // 新增：用户事件
    };

    console.log('🔧 页面信息:', pageInfo);

    // 使用增强插画服务生成个性化插画
    const imageUrl = await enhancedIllustrationService.generatePersonalizedIllustration({
      userAnswer: answer,
      questionContext: context?.currentPage?.question || '',
      pageNumber: pageId,
      baseCharacters: pageInfo.characters,
      referenceImages: getReferenceImages(context.currentPageIndex, allImages),
      storyData: currentStoryData, // 传递故事数据
      userEvents: userEvents // 传递用户事件
    });

    console.log(`✅ 增强版插画生成完成，URL: ${imageUrl}`);

    // 获取生成统计信息
    const stats = enhancedIllustrationService.getGenerationStats();
    console.log('📊 生成统计:', stats);

    return imageUrl;
  } catch (error) {
    console.error('❌ 增强版插画生成失败，回退到原始方法:', error);

    // 回退到原始生成方法
    try {
      console.log(`🔄 使用新的回退方法...`);
      const prompt = buildImagePrompt(answer, context, pageId, storyData || context?.storyData);
      const referenceImages = getReferenceImages(context.currentPageIndex, allImages);
      const keyContent = extractKeyContent(answer);
      const userEvents = extractUserEvents(answer);

      // 优先使用用户事件作为场景描述
      let sceneDescription;
      if (userEvents.length > 0) {
        sceneDescription = userEvents.join('，');
      } else {
        sceneDescription = `${keyContent.characters.join('和')}在${keyContent.sceneElements.join('和')}中${keyContent.actions.join('和')}`;
      }

      const imageUrl = await generateImage(prompt, referenceImages, answer, sceneDescription, storyData || context?.storyData);
      console.log(`✅ 回退方法插画生成完成，URL: ${imageUrl}`);
      return imageUrl;
    } catch (fallbackError) {
      console.error('❌ 回退方法也失败了:', fallbackError);
      throw fallbackError;
    }
  }
}

/**
 * 检查生成的插画是否与现有风格一致（增强版）
 * @param {string} generatedImageUrl - 生成的图像URL
 * @param {Array} referenceImages - 参考图像URL数组
 * @param {Array} expectedCharacters - 期望的角色列表
 * @returns {Promise<Object>} 一致性检查结果
 */
export async function checkStyleConsistency(generatedImageUrl, referenceImages, expectedCharacters = []) {
  try {
    console.log('🔍 使用增强版一致性检查器...');
    console.log('📸 检查图像:', generatedImageUrl);
    console.log('📚 参考图像:', referenceImages);
    console.log('👥 期望角色:', expectedCharacters);

    // 使用增强插画服务的一致性检查器
    const consistencyResult = await enhancedIllustrationService.consistencyChecker.checkImageConsistency(
      generatedImageUrl,
      expectedCharacters
    );

    console.log('✅ 一致性检查结果:', consistencyResult);

    // 返回详细的检查结果
    return {
      isConsistent: consistencyResult.isConsistent,
      score: consistencyResult.score,
      details: consistencyResult,
      // 保持向后兼容性
      legacy: consistencyResult.isConsistent
    };
  } catch (error) {
    console.error('❌ 增强版一致性检查失败，使用简化检查:', error);

    // 回退到简化检查
    console.log('🔍 回退到简化一致性检查...');

    // 简化的检查逻辑
    const isConsistent = referenceImages.length > 0 && generatedImageUrl && generatedImageUrl.length > 0;

    return {
      isConsistent,
      score: isConsistent ? 0.8 : 0.3,
      details: {
        method: 'fallback',
        hasReferences: referenceImages.length > 0,
        hasGeneratedImage: !!generatedImageUrl
      },
      legacy: isConsistent
    };
  }
}

/**
 * 清除特定页面的图像缓存
 * @param {number} pageId - 页面ID
 * @param {string} answer - 可选，特定回答的缓存
 */
export function clearImageCache(pageId, answer = null) {
  try {
    if (answer) {
      // 清除特定回答的缓存
      const cacheKey = generateCacheKey(pageId, answer);
      const metadataKey = `${cacheKey}_metadata`;

      localStorage.removeItem(cacheKey);
      localStorage.removeItem(metadataKey);

      console.log(`已清除页面${pageId}特定回答的图像缓存`);
    } else {
      // 清除页面所有缓存
      const keys = Object.keys(localStorage);
      const pageKeys = keys.filter(key => key.startsWith(`generated_image_page_${pageId}_`));

      pageKeys.forEach(key => localStorage.removeItem(key));

      console.log(`已清除页面${pageId}的所有图像缓存，共${pageKeys.length}项`);
    }
  } catch (error) {
    console.error('清除图像缓存失败:', error);
  }
}

/**
 * 清除所有页面的图像缓存
 */
export function clearAllImageCache() {
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);

    // 筛选出图像缓存键（包括元数据）
    const imageCacheKeys = keys.filter(key => key.startsWith('generated_image_page_'));

    // 删除所有图像缓存
    imageCacheKeys.forEach(key => localStorage.removeItem(key));

    console.log(`已清除所有图像缓存，共${imageCacheKeys.length}项`);
  } catch (error) {
    console.error('清除所有图像缓存失败:', error);
  }
}

/**
 * 清除旧会话的图像缓存
 * @param {number} maxAge - 最大缓存时间（毫秒），默认30分钟
 */
export function clearOldSessionCache(maxAge = 30 * 60 * 1000) {
  try {
    const currentTime = Date.now();
    const keys = Object.keys(localStorage);

    // 筛选出图像缓存的元数据键
    const metadataKeys = keys.filter(key =>
      key.startsWith('generated_image_page_') && key.endsWith('_metadata')
    );

    let clearedCount = 0;

    metadataKeys.forEach(metadataKey => {
      try {
        const metadata = JSON.parse(localStorage.getItem(metadataKey) || '{}');
        const cacheAge = currentTime - (metadata.timestamp || 0);

        if (cacheAge > maxAge) {
          // 清除过期的缓存项
          const cacheKey = metadataKey.replace('_metadata', '');
          localStorage.removeItem(cacheKey);
          localStorage.removeItem(metadataKey);
          clearedCount++;
        }
      } catch (error) {
        console.warn('清除过期缓存项失败:', metadataKey, error);
      }
    });

    if (clearedCount > 0) {
      console.log(`已清除${clearedCount}个过期的图像缓存项`);
    }
  } catch (error) {
    console.error('清除旧会话缓存失败:', error);
  }
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计信息
 */
export function getCacheStats() {
  try {
    const keys = Object.keys(localStorage);
    const imageCacheKeys = keys.filter(key =>
      key.startsWith('generated_image_page_') &&
      !key.endsWith('_metadata')
    );

    const metadataKeys = keys.filter(key =>
      key.startsWith('generated_image_page_') &&
      key.endsWith('_metadata')
    );

    // 计算总缓存大小（估算）
    let totalSize = 0;
    imageCacheKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) totalSize += value.length;
    });

    // 按页面分组统计
    const pageStats = {};
    metadataKeys.forEach(key => {
      try {
        const metadata = JSON.parse(localStorage.getItem(key) || '{}');
        const pageId = metadata.pageId;

        if (pageId) {
          if (!pageStats[pageId]) {
            pageStats[pageId] = { count: 0, lastGenerated: 0 };
          }
          pageStats[pageId].count++;
          pageStats[pageId].lastGenerated = Math.max(
            pageStats[pageId].lastGenerated,
            metadata.timestamp || 0
          );
        }
      } catch (e) {
        // 忽略解析错误
      }
    });

    return {
      totalImages: imageCacheKeys.length,
      totalSizeEstimate: totalSize,
      pageStats,
      oldestCache: Math.min(...metadataKeys.map(key => {
        try {
          const metadata = JSON.parse(localStorage.getItem(key) || '{}');
          return metadata.timestamp || Date.now();
        } catch (e) {
          return Date.now();
        }
      }))
    };
  } catch (error) {
    console.error('获取缓存统计失败:', error);
    return { totalImages: 0, totalSizeEstimate: 0, pageStats: {}, oldestCache: Date.now() };
  }
}

export default {
  generateIllustrationFromAnswer,
  checkStyleConsistency,
  clearImageCache,
  clearAllImageCache,
  getCacheStats,
  getPageCachedImages,
  buildImagePrompt,
  extractKeyContent,
  optimizePromptForImage2Image,
  buildInteractiveIllustrationPrompt
};
