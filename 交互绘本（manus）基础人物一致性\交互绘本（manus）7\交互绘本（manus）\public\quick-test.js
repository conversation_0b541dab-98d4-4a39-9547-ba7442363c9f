/**
 * 快速测试脚本 - 简化版本
 * 用于快速检查增强功能是否可用
 */

// 快速检查函数（支持异步加载）
async function quickCheck() {
    console.log('🔍 快速检查增强功能状态...');

    // 检查基础服务
    const basicServices = {
        'React应用': !!document.getElementById('root'),
        'Vite开发服务器': window.location.port === '5173',
        '控制台可用': !!console.log
    };

    console.log('\n📋 基础环境检查:');
    Object.entries(basicServices).forEach(([name, status]) => {
        console.log(`${status ? '✅' : '❌'} ${name}`);
    });

    // 检查基础服务
    const basicGlobalServices = [
        'liblibService',
        'storyIllustrationGenerator',
        'generateIllustrationFromAnswer',
        'extractKeyContent'
    ];

    console.log('\n📋 基础服务检查:');
    const basicServiceStatus = {};
    basicGlobalServices.forEach(service => {
        basicServiceStatus[service] = !!window[service];
        console.log(`${basicServiceStatus[service] ? '✅' : '❌'} ${service}`);
    });

    // 检查增强服务（可能需要异步加载）
    const enhancedServices = [
        'enhancedIllustrationService',
        'StyleManager',
        'CharacterManager',
        'SceneStyleController',
        'ConsistencyChecker',
        'EnhancedPromptManager'
    ];

    console.log('\n📋 增强服务检查:');
    const enhancedServiceStatus = {};
    enhancedServices.forEach(service => {
        enhancedServiceStatus[service] = !!window[service];
        console.log(`${enhancedServiceStatus[service] ? '✅' : '❌'} ${service}`);
    });

    // 如果增强服务未加载，尝试异步加载
    const enhancedLoaded = Object.values(enhancedServiceStatus).filter(Boolean).length;
    if (enhancedLoaded === 0 && window.loadEnhancedServices) {
        console.log('\n⏳ 检测到增强服务未加载，尝试异步加载...');
        try {
            const loadSuccess = await window.loadEnhancedServices();
            if (loadSuccess) {
                console.log('✅ 增强服务异步加载成功');
                // 重新检查
                enhancedServices.forEach(service => {
                    enhancedServiceStatus[service] = !!window[service];
                    console.log(`${enhancedServiceStatus[service] ? '✅' : '❌'} ${service} (重新检查)`);
                });
            } else {
                console.log('❌ 增强服务异步加载失败');
            }
        } catch (error) {
            console.log('❌ 异步加载过程中出错:', error.message);
        }
    }

    // 总结
    const totalBasicServices = basicGlobalServices.length;
    const totalEnhancedServices = enhancedServices.length;
    const loadedBasicServices = Object.values(basicServiceStatus).filter(Boolean).length;
    const loadedEnhancedServices = Object.values(enhancedServiceStatus).filter(Boolean).length;

    console.log(`\n📊 服务加载状态:`);
    console.log(`   基础服务: ${loadedBasicServices}/${totalBasicServices} (${Math.round(loadedBasicServices/totalBasicServices*100)}%)`);
    console.log(`   增强服务: ${loadedEnhancedServices}/${totalEnhancedServices} (${Math.round(loadedEnhancedServices/totalEnhancedServices*100)}%)`);

    if (loadedBasicServices === totalBasicServices && loadedEnhancedServices === totalEnhancedServices) {
        console.log('🎉 所有功能已准备就绪！');
        console.log('💡 可以运行: testBasicFeatures() 或 testAllPagesEnhancement()');
    } else if (loadedBasicServices === totalBasicServices) {
        console.log('✅ 基础功能可用');
        console.log('⚠️ 增强功能部分可用，可以继续测试基础功能');
    } else {
        console.log('❌ 基础功能未完全加载');
        console.log('🔧 请检查控制台是否有错误信息');
    }

    return {
        basicServices,
        basicServiceStatus,
        enhancedServiceStatus,
        totalLoadedBasic: loadedBasicServices,
        totalLoadedEnhanced: loadedEnhancedServices,
        totalBasicServices,
        totalEnhancedServices
    };
}

// 简单的功能测试
function testBasicFeatures() {
    console.log('🧪 测试基础功能...');
    
    try {
        // 测试StyleManager
        if (window.StyleManager) {
            const styleManager = new window.StyleManager();
            const prompt = styleManager.generateStylePrompt("测试场景");
            console.log('✅ StyleManager 工作正常');
            console.log('📝 示例提示词:', prompt.prompt.substring(0, 50) + '...');
        } else {
            console.log('❌ StyleManager 未加载');
        }
        
        // 测试CharacterManager
        if (window.CharacterManager) {
            const charManager = new window.CharacterManager();
            const characters = charManager.getAllCharacters();
            console.log('✅ CharacterManager 工作正常');
            console.log('👥 可用角色:', characters);
        } else {
            console.log('❌ CharacterManager 未加载');
        }
        
        // 测试liblibService
        if (window.liblibService) {
            const apiStatus = window.liblibService.getApiStatus();
            console.log('✅ liblibService 已加载');
            console.log('🔑 API状态:', apiStatus);
        } else {
            console.log('❌ liblibService 未加载');
        }
        
        console.log('✅ 基础功能测试完成');
        
    } catch (error) {
        console.error('❌ 基础功能测试失败:', error);
    }
}

// 等待并重试检查
function waitAndRetry(maxRetries = 5, interval = 2000) {
    let retries = 0;
    
    const check = () => {
        console.log(`🔄 第 ${retries + 1} 次检查...`);
        const result = quickCheck();
        
        if (result.totalLoaded === result.totalServices) {
            console.log('🎉 所有服务已加载完成！');
            testBasicFeatures();
            return;
        }
        
        retries++;
        if (retries < maxRetries) {
            console.log(`⏳ ${interval/1000}秒后重试...`);
            setTimeout(check, interval);
        } else {
            console.log('⏰ 达到最大重试次数');
            console.log('💡 建议手动刷新页面');
        }
    };
    
    check();
}

// 显示帮助信息
function showHelp() {
    console.log(`
🆘 增强功能测试帮助
==================

快速命令:
- quickCheck()           // 快速检查服务状态
- testBasicFeatures()    // 测试基础功能
- waitAndRetry()         // 等待服务加载并重试
- showHelp()             // 显示此帮助

故障排除:
1. 如果服务未加载，请刷新页面
2. 检查控制台是否有红色错误信息
3. 确认应用正在 http://localhost:5173/ 运行
4. 等待几秒钟让模块完全加载

测试流程:
1. 运行 quickCheck() 检查状态
2. 如果未完全加载，运行 waitAndRetry()
3. 加载完成后运行 testBasicFeatures()
4. 最后可以运行完整测试 testAllPagesEnhancement()
`);
}

// 导出到全局作用域
window.quickCheck = quickCheck;
window.testBasicFeatures = testBasicFeatures;
window.waitAndRetry = waitAndRetry;
window.showHelp = showHelp;

// 自动运行初始检查
console.log('🚀 快速测试脚本已加载');
showHelp();

// 延迟执行初始检查，给应用时间加载
setTimeout(() => {
    console.log('\n🔍 执行初始检查...');
    quickCheck();
}, 2000);
