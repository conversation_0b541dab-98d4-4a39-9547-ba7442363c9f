// 语音服务
import { Language } from './i18nService';

export interface VoiceOption {
  voice: SpeechSynthesisVoice;
  name: string;
  lang: string;
  gender: 'female' | 'male' | 'unknown';
  isChildVoice: boolean;
}

class SpeechService {
  private voices: SpeechSynthesisVoice[] = [];
  private selectedVoice: SpeechSynthesisVoice | null = null;
  private currentLanguage: Language = 'zh';
  private isInitialized = false;

  constructor() {
    this.initializeVoices();
  }

  private async initializeVoices(): Promise<void> {
    if ('speechSynthesis' in window) {
      // 等待语音列表加载
      const loadVoices = () => {
        this.voices = speechSynthesis.getVoices();
        this.selectBestVoice();
        this.isInitialized = true;
      };

      // 立即尝试加载
      loadVoices();

      // 如果没有语音，等待onvoiceschanged事件
      if (this.voices.length === 0) {
        speechSynthesis.onvoiceschanged = loadVoices;
      }
    }
  }

  private selectBestVoice(): void {
    if (this.voices.length === 0) return;

    const currentLang = this.currentLanguage === 'zh' ? 'zh' : 'en';
    
    // 优先选择小女孩音色的策略
    const voicePreferences = [
      // 中文语音优先级
      ...(currentLang === 'zh' ? [
        'Microsoft Xiaoxiao - Chinese (Simplified, PRC)', // Windows 小晓
        'Microsoft Yaoyao - Chinese (Simplified, PRC)',   // Windows 瑶瑶
        'Ting-Ting',                                       // macOS 婷婷
        'Sin-ji',                                          // macOS 欣吉
        'Mei-Jia',                                         // macOS 美佳
        'Google 普通话（中国大陆）',                        // Chrome
        'zh-CN',                                           // 通用中文
        'zh'                                               // 中文
      ] : []),
      // 英文语音优先级
      ...(currentLang === 'en' ? [
        'Microsoft Zira - English (United States)',       // Windows Zira (女声)
        'Microsoft Hazel - English (Great Britain)',      // Windows Hazel (女声)
        'Samantha',                                        // macOS Samantha (女声)
        'Karen',                                           // macOS Karen (女声)
        'Princess',                                        // macOS Princess (女声)
        'Google US English',                               // Chrome
        'en-US',                                           // 通用美式英语
        'en-GB',                                           // 英式英语
        'en'                                               // 英语
      ] : [])
    ];

    // 按优先级查找语音
    for (const preference of voicePreferences) {
      const voice = this.voices.find(v => 
        v.name.includes(preference) || 
        v.lang.includes(preference) ||
        v.name === preference
      );
      if (voice) {
        this.selectedVoice = voice;
        console.log(`🎤 选择语音: ${voice.name} (${voice.lang})`);
        return;
      }
    }

    // 如果没找到首选语音，选择第一个匹配语言的女声
    const femaleVoices = this.voices.filter(voice => {
      const isCorrectLang = voice.lang.startsWith(currentLang);
      const isFemale = this.isLikelyFemaleVoice(voice);
      return isCorrectLang && isFemale;
    });

    if (femaleVoices.length > 0) {
      this.selectedVoice = femaleVoices[0];
      console.log(`🎤 选择女声: ${this.selectedVoice.name} (${this.selectedVoice.lang})`);
      return;
    }

    // 最后选择任何匹配语言的语音
    const langVoices = this.voices.filter(voice => voice.lang.startsWith(currentLang));
    if (langVoices.length > 0) {
      this.selectedVoice = langVoices[0];
      console.log(`🎤 选择语音: ${this.selectedVoice.name} (${this.selectedVoice.lang})`);
    }
  }

  private isLikelyFemaleVoice(voice: SpeechSynthesisVoice): boolean {
    const femaleNames = [
      'xiaoxiao', 'yaoyao', 'ting-ting', 'sin-ji', 'mei-jia',
      'zira', 'hazel', 'samantha', 'karen', 'princess', 'susan',
      'female', 'woman', 'girl', 'lady'
    ];
    
    const voiceName = voice.name.toLowerCase();
    return femaleNames.some(name => voiceName.includes(name));
  }

  setLanguage(language: Language): void {
    this.currentLanguage = language;
    this.selectBestVoice();
  }

  getAvailableVoices(): VoiceOption[] {
    const currentLang = this.currentLanguage === 'zh' ? 'zh' : 'en';
    
    return this.voices
      .filter(voice => voice.lang.startsWith(currentLang))
      .map(voice => ({
        voice,
        name: voice.name,
        lang: voice.lang,
        gender: this.isLikelyFemaleVoice(voice) ? 'female' : 'male',
        isChildVoice: this.isLikelyChildVoice(voice)
      }))
      .sort((a, b) => {
        // 优先排序：儿童声音 > 女声 > 其他
        if (a.isChildVoice && !b.isChildVoice) return -1;
        if (!a.isChildVoice && b.isChildVoice) return 1;
        if (a.gender === 'female' && b.gender !== 'female') return -1;
        if (a.gender !== 'female' && b.gender === 'female') return 1;
        return a.name.localeCompare(b.name);
      });
  }

  private isLikelyChildVoice(voice: SpeechSynthesisVoice): boolean {
    const childNames = ['xiaoxiao', 'child', 'kid', 'young', 'little'];
    const voiceName = voice.name.toLowerCase();
    return childNames.some(name => voiceName.includes(name));
  }

  setVoice(voice: SpeechSynthesisVoice): void {
    this.selectedVoice = voice;
    localStorage.setItem('storybook-voice', voice.name);
    console.log(`🎤 用户选择语音: ${voice.name} (${voice.lang})`);
  }

  speak(text: string, options?: {
    rate?: number;
    pitch?: number;
    volume?: number;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: any) => void;
  }): void {
    if (!('speechSynthesis' in window)) {
      console.error('浏览器不支持语音合成');
      options?.onError?.('浏览器不支持语音合成');
      return;
    }

    // 取消之前的语音
    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    
    // 设置语音
    if (this.selectedVoice) {
      utterance.voice = this.selectedVoice;
    }

    // 设置语音属性 - 针对小女孩音色优化
    utterance.rate = options?.rate ?? 0.85;  // 稍慢一点，更适合儿童
    utterance.pitch = options?.pitch ?? 1.1; // 稍高一点，更像小女孩
    utterance.volume = options?.volume ?? 1.0;

    // 设置语言
    const lang = this.currentLanguage === 'zh' ? 'zh-CN' : 'en-US';
    utterance.lang = lang;

    // 设置事件处理
    utterance.onstart = () => {
      console.log(`🎤 开始朗读: ${text.substring(0, 50)}...`);
      options?.onStart?.();
    };

    utterance.onend = () => {
      console.log('🎤 朗读完成');
      options?.onEnd?.();
    };

    utterance.onerror = (event) => {
      console.error('🎤 朗读错误:', event);
      options?.onError?.(event);
    };

    speechSynthesis.speak(utterance);
  }

  stop(): void {
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
    }
  }

  isSpeaking(): boolean {
    return 'speechSynthesis' in window && speechSynthesis.speaking;
  }

  // 语音识别
  startSpeechRecognition(options: {
    onResult: (transcript: string) => void;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: string) => void;
  }): void {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      options.onError?.('浏览器不支持语音识别功能');
      return;
    }

    const SpeechRecognitionConstructor = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    const recognition = new SpeechRecognitionConstructor();

    // 设置语言
    const lang = this.currentLanguage === 'zh' ? 'zh-CN' : 'en-US';
    recognition.lang = lang;
    recognition.continuous = false;
    recognition.interimResults = true;

    recognition.onstart = () => {
      console.log('🎤 开始语音识别');
      options.onStart?.();
    };

    recognition.onresult = (event: any) => {
      let transcript = '';
      for (let i = 0; i < event.results.length; i++) {
        if (event.results[i][0] && event.results[i][0].transcript) {
          transcript += event.results[i][0].transcript;
        }
      }
      options.onResult(transcript);
    };

    recognition.onerror = (event: any) => {
      console.error('🎤 语音识别错误:', event.error);
      options.onError?.(event.error);
    };

    recognition.onend = () => {
      console.log('🎤 语音识别结束');
      options.onEnd?.();
    };

    recognition.start();
  }

  // 等待语音初始化完成
  async waitForInitialization(): Promise<void> {
    if (this.isInitialized) return;
    
    return new Promise((resolve) => {
      const checkInitialized = () => {
        if (this.isInitialized) {
          resolve();
        } else {
          setTimeout(checkInitialized, 100);
        }
      };
      checkInitialized();
    });
  }
}

export const speechService = new SpeechService();
export default speechService;
