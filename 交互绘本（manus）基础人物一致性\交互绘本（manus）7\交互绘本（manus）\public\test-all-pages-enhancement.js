/**
 * 测试所有页面类型的增强风格一致性功能
 * 包括交互页面和非交互页面
 */

// 等待服务加载的辅助函数
function waitForServices(maxWaitTime = 10000) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        const checkServices = () => {
            if (window.liblibService && window.enhancedIllustrationService) {
                console.log('✅ 服务已加载完成');
                resolve(true);
            } else if (Date.now() - startTime > maxWaitTime) {
                console.log('⏰ 等待服务超时');
                resolve(false);
            } else {
                setTimeout(checkServices, 100);
            }
        };
        checkServices();
    });
}

// 测试所有页面增强功能的主函数
async function testAllPagesEnhancement() {
    console.log('🧪 开始测试所有页面类型的增强风格一致性功能...');

    // 等待服务加载
    console.log('⏳ 等待服务加载...');
    const servicesLoaded = await waitForServices();

    if (!servicesLoaded) {
        console.log('❌ 服务加载超时，请刷新页面重试');
        return;
    }

    const testResults = {
        interactivePages: { tested: false, success: false, details: null },
        nonInteractivePages: { tested: false, success: false, details: null },
        storyGeneration: { tested: false, success: false, details: null },
        apiConnection: { tested: false, success: false, details: null }
    };

    try {
        // 1. 测试API连接
        console.log('\n🌐 测试API连接...');
        testResults.apiConnection = await testApiConnectionStatus();

        // 2. 测试非交互页面增强功能
        console.log('\n📖 测试非交互页面增强功能...');
        testResults.nonInteractivePages = await testNonInteractivePageEnhancement();

        // 3. 测试交互页面增强功能
        console.log('\n🎭 测试交互页面增强功能...');
        testResults.interactivePages = await testInteractivePageEnhancement();

        // 4. 测试故事生成增强功能
        console.log('\n📚 测试故事生成增强功能...');
        testResults.storyGeneration = await testStoryGenerationEnhancement();

        // 5. 显示测试总结
        displayTestSummary(testResults);

        return testResults;

    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return testResults;
    }
}

// 测试API连接状态
async function testApiConnectionStatus() {
    try {
        if (!window.liblibService) {
            return { tested: true, success: false, details: 'LIBLIB服务未找到' };
        }

        const apiStatus = window.liblibService.getApiStatus();
        console.log('📊 API状态:', apiStatus);

        if (!apiStatus.isInitialized) {
            return { 
                tested: true, 
                success: false, 
                details: 'API密钥未初始化，请检查.env文件' 
            };
        }

        // 尝试测试连接
        try {
            const connectionTest = await window.liblibService.testConnection();
            return {
                tested: true,
                success: connectionTest.success,
                details: connectionTest.success ? 
                    `连接成功，测试图像: ${connectionTest.testImageUrl}` : 
                    connectionTest.message
            };
        } catch (testError) {
            return {
                tested: true,
                success: false,
                details: `连接测试失败: ${testError.message}`
            };
        }

    } catch (error) {
        return {
            tested: true,
            success: false,
            details: `API测试失败: ${error.message}`
        };
    }
}

// 测试非交互页面增强功能
async function testNonInteractivePageEnhancement() {
    try {
        // 检查故事插图生成器是否可用
        if (!window.storyIllustrationGenerator) {
            return {
                tested: true,
                success: false,
                details: '故事插图生成器未找到'
            };
        }

        console.log('✅ 故事插图生成器已找到');

        // 检查增强插画服务是否可用
        if (!window.enhancedIllustrationService) {
            return {
                tested: true,
                success: false,
                details: '增强插画服务未找到'
            };
        }

        console.log('✅ 增强插画服务已找到');

        // 模拟测试页面数据
        const testPageData = {
            id: 1,
            content: "小熊波波在森林中遇到了新朋友小兔子，他们一起开心地玩耍。",
            isInteractive: false
        };

        const testStoryData = {
            title: "测试故事",
            theme: "友谊",
            characters: ["小熊波波", "小兔子"],
            ageGroup: "6-8岁"
        };

        console.log('🔧 测试页面数据:', testPageData);

        // 测试页面内容分析功能
        if (window.storyIllustrationGenerator.analyzePageContent) {
            const analysis = window.storyIllustrationGenerator.analyzePageContent(testPageData, testStoryData);
            console.log('✅ 页面内容分析成功:', analysis);
        }

        return {
            tested: true,
            success: true,
            details: '非交互页面增强功能测试通过，包括内容分析和服务集成'
        };

    } catch (error) {
        return {
            tested: true,
            success: false,
            details: `非交互页面测试失败: ${error.message}`
        };
    }
}

// 测试交互页面增强功能
async function testInteractivePageEnhancement() {
    try {
        // 检查交互插画生成功能
        if (!window.generateIllustrationFromAnswer) {
            return {
                tested: true,
                success: false,
                details: '交互插画生成功能未找到'
            };
        }

        console.log('✅ 交互插画生成功能已找到');

        // 检查增强插画服务
        if (!window.enhancedIllustrationService) {
            return {
                tested: true,
                success: false,
                details: '增强插画服务未找到'
            };
        }

        console.log('✅ 增强插画服务已找到');

        // 测试用户回答分析
        const testAnswer = "我喜欢和小熊波波一起在森林里玩，我们可以一起分享快乐";
        
        // 检查回答分析功能
        if (window.extractKeyContent) {
            const keyContent = window.extractKeyContent(testAnswer);
            console.log('✅ 用户回答分析成功:', keyContent);
        }

        return {
            tested: true,
            success: true,
            details: '交互页面增强功能测试通过，包括回答分析和个性化生成'
        };

    } catch (error) {
        return {
            tested: true,
            success: false,
            details: `交互页面测试失败: ${error.message}`
        };
    }
}

// 测试故事生成增强功能
async function testStoryGenerationEnhancement() {
    try {
        // 检查故事生成相关服务
        const services = [
            'storyIllustrationGenerator',
            'enhancedIllustrationService'
        ];

        const missingServices = services.filter(service => !window[service]);
        
        if (missingServices.length > 0) {
            return {
                tested: true,
                success: false,
                details: `缺少服务: ${missingServices.join(', ')}`
            };
        }

        console.log('✅ 所有故事生成相关服务已找到');

        // 测试故事上下文设置
        const testStoryContext = {
            title: "测试故事",
            theme: "友谊",
            characters: ["小熊波波", "小兔子", "小松鼠"]
        };

        if (window.enhancedIllustrationService.setStoryContext) {
            window.enhancedIllustrationService.setStoryContext(testStoryContext);
            console.log('✅ 故事上下文设置成功');
        }

        // 获取生成统计
        if (window.enhancedIllustrationService.getGenerationStats) {
            const stats = window.enhancedIllustrationService.getGenerationStats();
            console.log('✅ 生成统计获取成功:', stats);
        }

        return {
            tested: true,
            success: true,
            details: '故事生成增强功能测试通过，包括上下文管理和统计功能'
        };

    } catch (error) {
        return {
            tested: true,
            success: false,
            details: `故事生成测试失败: ${error.message}`
        };
    }
}

// 显示测试总结
function displayTestSummary(testResults) {
    console.log('\n📊 测试总结报告');
    console.log('==================');

    let totalTests = 0;
    let passedTests = 0;

    Object.entries(testResults).forEach(([testName, result]) => {
        if (result.tested) {
            totalTests++;
            if (result.success) {
                passedTests++;
                console.log(`✅ ${testName}: ${result.details}`);
            } else {
                console.log(`❌ ${testName}: ${result.details}`);
            }
        } else {
            console.log(`⏸️ ${testName}: 未测试`);
        }
    });

    console.log('\n📈 测试统计:');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${totalTests - passedTests} ❌`);
    console.log(`成功率: ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%`);

    // 提供使用建议
    console.log('\n💡 使用建议:');
    
    if (testResults.apiConnection.success) {
        console.log('✅ API连接正常，可以进行实际图像生成测试');
    } else {
        console.log('⚠️ API连接异常，请检查API密钥配置');
    }

    if (testResults.nonInteractivePages.success && testResults.interactivePages.success) {
        console.log('✅ 所有页面类型都支持增强功能');
        console.log('🎯 建议测试流程:');
        console.log('   1. 点击"生成新故事"测试非交互页面');
        console.log('   2. 导航到交互页面测试个性化插画');
        console.log('   3. 观察控制台日志查看增强功能启用情况');
    } else {
        console.log('⚠️ 部分页面类型的增强功能可能不可用');
    }
}

// 显示功能覆盖范围
function showFeatureCoverage() {
    console.log(`
🎯 增强风格一致性功能覆盖范围
================================

📖 非交互页面 (故事页面):
   ✅ 自动角色识别和分析
   ✅ 场景类型智能检测
   ✅ 情绪和动作提取
   ✅ 风格一致性保证
   ✅ 角色参考图像管理

🎭 交互页面 (用户回答页面):
   ✅ 个性化插画生成
   ✅ 用户回答内容分析
   ✅ 基于回答的场景构建
   ✅ 参考图像智能选择
   ✅ 一致性检查和验证

📚 故事生成:
   ✅ 整体故事上下文管理
   ✅ 批量插图生成优化
   ✅ 角色种子图像生成
   ✅ 生成统计和质量监控

🔧 技术特性:
   ✅ 自动回退机制
   ✅ 详细日志记录
   ✅ 性能统计
   ✅ 错误处理和恢复

测试命令:
- testAllPagesEnhancement()  // 完整测试
- showFeatureCoverage()      // 显示功能覆盖
`);
}

// 检查服务状态的函数
function checkServiceStatus() {
    console.log('🔍 检查服务状态...');

    const services = [
        'liblibService',
        'storyIllustrationGenerator',
        'enhancedIllustrationService',
        'generateIllustrationFromAnswer',
        'extractKeyContent',
        'StyleManager',
        'CharacterManager',
        'SceneStyleController',
        'ConsistencyChecker',
        'EnhancedPromptManager'
    ];

    const status = {};
    services.forEach(serviceName => {
        status[serviceName] = !!window[serviceName];
        console.log(`${status[serviceName] ? '✅' : '❌'} ${serviceName}: ${status[serviceName] ? '已加载' : '未找到'}`);
    });

    const loadedCount = Object.values(status).filter(Boolean).length;
    console.log(`\n📊 服务加载状态: ${loadedCount}/${services.length} (${Math.round(loadedCount/services.length*100)}%)`);

    if (loadedCount === services.length) {
        console.log('🎉 所有服务已成功加载！');
    } else {
        console.log('⚠️ 部分服务未加载，请刷新页面重试');
    }

    return status;
}

// 将函数添加到全局作用域
window.testAllPagesEnhancement = testAllPagesEnhancement;
window.showFeatureCoverage = showFeatureCoverage;
window.checkServiceStatus = checkServiceStatus;

// 自动检查服务状态
setTimeout(() => {
    checkServiceStatus();
}, 1000);

// 自动显示功能覆盖范围
showFeatureCoverage();
