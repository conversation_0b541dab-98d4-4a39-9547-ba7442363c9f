/**
 * 风格一致性策略集成测试
 * 基于 style control.txt 文档的策略验证
 */

import enhancedIllustrationService from '../services/enhancedIllustrationService.js';
import StyleManager from '../services/styleManager.js';
import CharacterManager from '../services/characterManager.js';
import SceneStyleController from '../services/sceneStyleController.js';
import ConsistencyChecker from '../services/consistencyChecker.js';
import EnhancedPromptManager from '../services/enhancedPromptManager.js';

class StyleConsistencyTest {
    constructor() {
        this.testResults = [];
        this.styleManager = new StyleManager();
        this.characterManager = new CharacterManager();
        this.sceneController = new SceneStyleController();
        this.consistencyChecker = new ConsistencyChecker();
        this.promptManager = new EnhancedPromptManager();
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 开始运行风格一致性策略测试...');
        
        const tests = [
            this.testStyleManager,
            this.testCharacterManager,
            this.testSceneController,
            this.testConsistencyChecker,
            this.testPromptManager,
            this.testEnhancedIllustrationService,
            this.testIntegration
        ];

        for (const test of tests) {
            try {
                await test.call(this);
            } catch (error) {
                console.error(`❌ 测试失败: ${test.name}`, error);
                this.recordTestResult(test.name, false, error.message);
            }
        }

        this.printTestSummary();
        return this.testResults;
    }

    /**
     * 测试风格管理器
     */
    async testStyleManager() {
        console.log('🎨 测试风格管理器...');

        // 测试基础提示词生成
        const stylePrompt = this.styleManager.generateStylePrompt("小熊波波在森林中玩耍");
        
        this.assert(
            stylePrompt.prompt.includes("children's book illustration"),
            "风格提示词应包含儿童插画风格"
        );
        
        this.assert(
            stylePrompt.negativePrompt.includes("dark"),
            "负面提示词应包含不适合的元素"
        );

        // 测试情绪调整
        const emotionAdjustment = this.styleManager.getEmotionStyleAdjustment("happy");
        this.assert(
            emotionAdjustment.colorAdjustment.includes("bright"),
            "快乐情绪应使用明亮色彩"
        );

        // 测试交互页面风格
        const interactiveStyle = this.styleManager.getInteractivePageStyle("question");
        this.assert(
            interactiveStyle.background.includes("simple"),
            "交互页面应使用简单背景"
        );

        this.recordTestResult('StyleManager', true, '所有风格管理器测试通过');
    }

    /**
     * 测试角色管理器
     */
    async testCharacterManager() {
        console.log('👥 测试角色管理器...');

        // 测试角色描述生成
        const characterDesc = this.characterManager.getCharacterDescription("小熊波波", "happy", "playing");
        
        this.assert(
            characterDesc.includes("small brown bear cub"),
            "角色描述应包含基本特征"
        );
        
        this.assert(
            characterDesc.includes("smiling brightly"),
            "快乐情绪应反映在描述中"
        );

        // 测试多角色场景
        const multiCharDesc = this.characterManager.generateMultiCharacterDescription([
            { name: "小熊波波", emotion: "happy", action: "playing" },
            { name: "小兔子", emotion: "excited", action: "running" }
        ]);
        
        this.assert(
            multiCharDesc.includes("and"),
            "多角色描述应正确连接"
        );

        // 测试种子图像描述
        const seedDesc = this.characterManager.generateSeedImageDescription("小熊波波");
        this.assert(
            seedDesc.includes("front view"),
            "种子图像应包含正面视图要求"
        );

        this.recordTestResult('CharacterManager', true, '所有角色管理器测试通过');
    }

    /**
     * 测试场景风格控制器
     */
    async testSceneController() {
        console.log('🏞️ 测试场景风格控制器...');

        // 测试场景提示词生成
        const scenePrompt = this.sceneController.generateScenePrompt(
            "forest",
            [{ name: "小熊波波", emotion: "curious", action: "exploring" }],
            "discovering new friends"
        );

        this.assert(
            scenePrompt.prompt.includes("peaceful forest clearing"),
            "森林场景应包含正确环境描述"
        );

        this.assert(
            scenePrompt.sceneInfo.sceneType === "forest",
            "场景信息应正确记录"
        );

        // 测试交互场景
        const interactivePrompt = this.sceneController.generateInteractiveScenePrompt(
            "question",
            [{ name: "小熊波波", emotion: "shy", action: "thinking" }],
            "answering a question about friendship"
        );

        this.assert(
            interactivePrompt.interactionInfo.isInteractive === true,
            "交互场景应标记为交互式"
        );

        // 测试场景配置验证
        const validation = this.sceneController.validateSceneConfiguration("forest", [
            { name: "小熊波波" }
        ]);

        this.assert(
            validation.isValid === true,
            "有效的场景配置应通过验证"
        );

        this.recordTestResult('SceneStyleController', true, '所有场景控制器测试通过');
    }

    /**
     * 测试一致性检查器
     */
    async testConsistencyChecker() {
        console.log('🔍 测试一致性检查器...');

        // 测试角色参考设置
        const testImageUrl = "https://example.com/test-image.jpg";
        this.consistencyChecker.setCharacterReference("小熊波波", testImageUrl);

        this.assert(
            this.consistencyChecker.hasCharacterReference("小熊波波"),
            "应能正确设置和检查角色参考"
        );

        // 测试一致性检查
        const consistencyResult = await this.consistencyChecker.checkImageConsistency(
            testImageUrl,
            ["小熊波波"]
        );

        this.assert(
            typeof consistencyResult.isConsistent === 'boolean',
            "一致性检查应返回布尔结果"
        );

        this.assert(
            typeof consistencyResult.score === 'number',
            "一致性检查应返回数值分数"
        );

        // 测试质量报告
        const qualityReport = this.consistencyChecker.getQualityReport();
        this.assert(
            typeof qualityReport.totalImages === 'number',
            "质量报告应包含图像数量统计"
        );

        this.recordTestResult('ConsistencyChecker', true, '所有一致性检查器测试通过');
    }

    /**
     * 测试增强提示词管理器
     */
    async testPromptManager() {
        console.log('📝 测试增强提示词管理器...');

        // 设置故事上下文
        const storyContext = {
            title: "小熊波波的友谊冒险",
            theme: "友谊",
            characters: ["小熊波波", "小兔子"]
        };
        this.promptManager.setStoryContext(storyContext);

        // 测试页面提示词生成
        const pageInfo = {
            pageNumber: 1,
            sceneType: "forest",
            characters: [{ name: "小熊波波", emotion: "curious", action: "exploring" }],
            isInteractive: false
        };

        const enhancedPrompt = this.promptManager.generateEnhancedImagePrompt(pageInfo);
        
        this.assert(
            enhancedPrompt.prompt.includes("children's book illustration"),
            "增强提示词应包含风格要求"
        );

        this.assert(
            enhancedPrompt.pageInfo.pageNumber === 1,
            "页面信息应正确记录"
        );

        // 测试角色种子提示词
        const seedPrompt = this.promptManager.generateCharacterSeedPrompt("小熊波波");
        this.assert(
            seedPrompt.characterInfo.isSeedImage === true,
            "种子提示词应标记为种子图像"
        );

        // 测试个性化提示词
        const personalizedPrompt = this.promptManager.generatePersonalizedIllustrationPrompt({
            userAnswer: "我喜欢和朋友一起玩",
            questionContext: "友谊",
            pageNumber: 4,
            baseCharacters: [{ name: "小熊波波", emotion: "happy" }]
        });

        this.assert(
            personalizedPrompt.personalizationInfo.isPersonalized === true,
            "个性化提示词应标记为个性化"
        );

        this.recordTestResult('EnhancedPromptManager', true, '所有提示词管理器测试通过');
    }

    /**
     * 测试增强插画服务
     */
    async testEnhancedIllustrationService() {
        console.log('🖼️ 测试增强插画服务...');

        // 设置故事上下文
        const storyContext = {
            title: "测试故事",
            characters: ["小熊波波", "小兔子"]
        };
        enhancedIllustrationService.setStoryContext(storyContext);

        // 测试服务状态
        const status = enhancedIllustrationService.promptManager.getManagerStatus();
        this.assert(
            status.hasStoryContext === true,
            "应正确设置故事上下文"
        );

        // 测试生成统计
        const stats = enhancedIllustrationService.getGenerationStats();
        this.assert(
            typeof stats.totalGenerated === 'number',
            "统计信息应包含生成数量"
        );

        this.recordTestResult('EnhancedIllustrationService', true, '所有增强插画服务测试通过');
    }

    /**
     * 测试整体集成
     */
    async testIntegration() {
        console.log('🔗 测试整体集成...');

        // 模拟完整的插画生成流程
        const pageInfo = {
            pageNumber: 1,
            content: "小熊波波在森林中遇到了新朋友",
            sceneType: "forest",
            characters: [
                { name: "小熊波波", emotion: "curious", action: "meeting" },
                { name: "小兔子", emotion: "friendly", action: "greeting" }
            ],
            isInteractive: false
        };

        // 测试场景提示词生成
        const scenePrompt = this.sceneController.generatePageScenePrompt(pageInfo);
        this.assert(
            scenePrompt.sceneInfo.characters.includes("小熊波波"),
            "集成测试应正确处理角色信息"
        );

        // 测试风格一致性
        this.assert(
            scenePrompt.prompt.includes("children's book illustration"),
            "集成测试应保持风格一致性"
        );

        this.recordTestResult('Integration', true, '所有集成测试通过');
    }

    /**
     * 断言辅助函数
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`断言失败: ${message}`);
        }
    }

    /**
     * 记录测试结果
     */
    recordTestResult(testName, passed, message) {
        this.testResults.push({
            testName,
            passed,
            message,
            timestamp: Date.now()
        });

        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
    }

    /**
     * 打印测试总结
     */
    printTestSummary() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;

        console.log('\n📊 测试总结:');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests} ✅`);
        console.log(`失败: ${failedTests} ❌`);
        console.log(`成功率: ${Math.round((passedTests / totalTests) * 100)}%`);

        if (failedTests > 0) {
            console.log('\n❌ 失败的测试:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => console.log(`  - ${r.testName}: ${r.message}`));
        }
    }
}

// 导出测试类
export default StyleConsistencyTest;

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location) {
    // 浏览器环境
    window.runStyleConsistencyTest = async () => {
        const test = new StyleConsistencyTest();
        return await test.runAllTests();
    };
    
    console.log('🧪 风格一致性测试已加载，运行 window.runStyleConsistencyTest() 开始测试');
}
