/**
 * 分析调试工具
 * 用于测试和调试OpenAI分析功能
 */

import openAIService from '../services/openAIService.js';

/**
 * 测试OpenAI分析功能
 * @param {Object} testData - 测试数据
 */
export async function testOpenAIAnalysis(testData = null) {
  console.log('🧪 开始测试OpenAI分析功能...');

  // 使用默认测试数据（如果没有提供）
  const defaultTestData = {
    questions: [
      "如果你在游乐场看到一个新朋友独自坐在秋千上，你会怎么做？",
      "当你和朋友们一起玩游戏时，如果有新的小朋友想要加入，你会怎么办？",
      "想象一下，你的好朋友今天看起来很难过，你觉得作为朋友应该怎么帮助他？"
    ],
    answers: [
      "我会走过去跟他打招呼，问他想不想一起玩",
      "我会邀请他一起玩，告诉他游戏规则",
      "我会问他怎么了，然后安慰他，陪他一起玩"
    ],
    storyData: {
      title: "小熊波波的友谊之旅",
      theme: "友谊与社交技能",
      ageGroup: "6-8岁",
      characters: [
        {
          name: "小熊波波",
          description: "一只可爱的小棕熊，有着温暖的棕色毛发，圆圆的脸庞，大大的黑色眼睛，红色上衣，蓝色短裤"
        },
        {
          name: "小兔莉莉",
          description: "一只友善的小白兔，有着长长的耳朵，温柔的眼神，粉色的连衣裙"
        }
      ],
      pages: [
        {
          id: 1,
          content: "小熊波波来到了森林游乐场，看到了许多小动物在快乐地玩耍。",
          isInteractive: false
        },
        {
          id: 2,
          content: "波波想要交新朋友，但是不知道该怎么开始。",
          isInteractive: true,
          question: "如果你在游乐场看到一个新朋友独自坐在秋千上，你会怎么做？"
        }
      ]
    },
    userResponses: [
      { pageId: 2, response: "我会走过去跟他打招呼，问他想不想一起玩" }
    ]
  };

  const data = testData || defaultTestData;

  try {
    // 1. 检查API密钥状态
    console.log('🔑 检查API密钥状态...');
    const isInitialized = openAIService.isApiKeyInitialized();
    console.log('API密钥状态:', isInitialized ? '✅ 已初始化' : '❌ 未初始化');

    if (!isInitialized) {
      console.error('❌ OpenAI API密钥未设置，无法进行测试');
      return {
        success: false,
        error: 'API密钥未设置',
        suggestion: '请在应用中设置OpenAI API密钥'
      };
    }

    // 2. 测试增强版分析
    console.log('🚀 测试增强版分析方法...');
    let enhancedResult = null;
    try {
      if (typeof openAIService.analyzeAutismPerformanceWithContext === 'function') {
        enhancedResult = await openAIService.analyzeAutismPerformanceWithContext(
          data.questions,
          data.answers,
          data.storyData,
          data.userResponses
        );
        console.log('✅ 增强版分析成功');
      } else {
        console.warn('⚠️ 增强版分析方法不可用');
      }
    } catch (enhancedError) {
      console.error('❌ 增强版分析失败:', enhancedError);
    }

    // 3. 测试标准分析（作为对比）
    console.log('🔄 测试标准分析方法...');
    let standardResult = null;
    try {
      standardResult = await openAIService.analyzeAutismPerformance(
        data.questions,
        data.answers,
        data.storyData.theme
      );
      console.log('✅ 标准分析成功');
    } catch (standardError) {
      console.error('❌ 标准分析失败:', standardError);
    }

    // 4. 比较结果
    const comparison = compareAnalysisResults(enhancedResult, standardResult);

    return {
      success: true,
      enhancedResult,
      standardResult,
      comparison,
      recommendations: generateTestRecommendations(enhancedResult, standardResult)
    };

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
}

/**
 * 比较两个分析结果
 */
function compareAnalysisResults(enhanced, standard) {
  const comparison = {
    bothSuccessful: !!(enhanced && standard),
    enhancedOnly: !!(enhanced && !standard),
    standardOnly: !!(!enhanced && standard),
    bothFailed: !enhanced && !standard
  };

  if (enhanced && standard) {
    comparison.detailLevel = {
      enhanced: Object.keys(enhanced).length,
      standard: Object.keys(standard).length
    };

    comparison.hasAdvancedFeatures = {
      enhanced: !!(enhanced.detailed_scores && enhanced.intervention_plan),
      standard: !!(standard.detailed_scores && standard.intervention_plan)
    };
  }

  return comparison;
}

/**
 * 生成测试建议
 */
function generateTestRecommendations(enhanced, standard) {
  const recommendations = [];

  if (!enhanced && !standard) {
    recommendations.push('检查OpenAI API密钥是否正确设置');
    recommendations.push('检查网络连接是否正常');
    recommendations.push('查看控制台错误日志获取详细信息');
  } else if (enhanced && !standard) {
    recommendations.push('增强版分析工作正常，标准分析可能有问题');
  } else if (!enhanced && standard) {
    recommendations.push('标准分析工作正常，增强版分析需要检查');
    recommendations.push('确认增强版分析方法是否正确实现');
  } else {
    recommendations.push('两种分析方法都工作正常');
    recommendations.push('建议在生产环境中使用增强版分析');
  }

  return recommendations;
}

/**
 * 快速测试函数（在浏览器控制台中使用）
 */
export function quickTest() {
  console.log('🚀 启动快速测试...');
  testOpenAIAnalysis().then(result => {
    console.log('📊 测试结果:', result);
  }).catch(error => {
    console.error('❌ 测试失败:', error);
  });
}

// 导出到全局作用域（用于调试）
if (typeof window !== 'undefined') {
  window.testOpenAIAnalysis = testOpenAIAnalysis;
  window.quickTest = quickTest;
}

export default {
  testOpenAIAnalysis,
  quickTest
};
