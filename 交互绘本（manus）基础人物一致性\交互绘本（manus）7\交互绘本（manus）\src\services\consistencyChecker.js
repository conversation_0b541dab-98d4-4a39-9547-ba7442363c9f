/**
 * 一致性检测器 - 检查图像和风格一致性
 * 基于 style control.txt 文档的策略实现
 * 注意：这是浏览器端的简化版本，主要用于基础一致性检查
 */

class ConsistencyChecker {
    constructor() {
        this.referenceImages = new Map(); // 存储参考图像信息
        this.consistencyThreshold = 0.7; // 一致性阈值
        this.qualityMetrics = []; // 质量指标记录
    }

    /**
     * 设置角色参考图像
     * @param {string} characterName - 角色名称
     * @param {string} imageUrl - 图像URL
     * @param {Object} metadata - 图像元数据
     */
    setCharacterReference(characterName, imageUrl, metadata = {}) {
        this.referenceImages.set(characterName, {
            url: imageUrl,
            metadata,
            timestamp: Date.now()
        });
        
        console.log(`已设置角色 ${characterName} 的参考图像`);
    }

    /**
     * 获取角色参考图像
     * @param {string} characterName - 角色名称
     * @returns {Object|null} 参考图像信息
     */
    getCharacterReference(characterName) {
        return this.referenceImages.get(characterName) || null;
    }

    /**
     * 检查是否有角色参考图像
     * @param {string} characterName - 角色名称
     * @returns {boolean} 是否有参考图像
     */
    hasCharacterReference(characterName) {
        return this.referenceImages.has(characterName);
    }

    /**
     * 基础图像一致性检查（简化版本）
     * @param {string} newImageUrl - 新图像URL
     * @param {Array} expectedCharacters - 期望的角色列表
     * @returns {Promise<Object>} 一致性检查结果
     */
    async checkImageConsistency(newImageUrl, expectedCharacters = []) {
        try {
            const results = {
                isConsistent: true,
                score: 1.0,
                issues: [],
                suggestions: [],
                characterChecks: {}
            };

            // 检查每个期望角色的一致性
            for (const character of expectedCharacters) {
                const characterName = typeof character === 'string' ? character : character.name;
                const characterCheck = await this.checkCharacterConsistency(newImageUrl, characterName);
                results.characterChecks[characterName] = characterCheck;
                
                if (!characterCheck.isConsistent) {
                    results.isConsistent = false;
                    results.issues.push(`角色 ${characterName} 一致性不足`);
                }
            }

            // 计算总体一致性分数
            const characterScores = Object.values(results.characterChecks).map(c => c.score);
            if (characterScores.length > 0) {
                results.score = characterScores.reduce((sum, score) => sum + score, 0) / characterScores.length;
            }

            // 记录质量指标
            this.recordQualityMetrics(newImageUrl, results);

            return results;
        } catch (error) {
            console.error('一致性检查失败:', error);
            return {
                isConsistent: false,
                score: 0,
                issues: [`一致性检查失败: ${error.message}`],
                suggestions: ['请重新生成图像'],
                characterChecks: {}
            };
        }
    }

    /**
     * 检查单个角色的一致性
     * @param {string} newImageUrl - 新图像URL
     * @param {string} characterName - 角色名称
     * @returns {Promise<Object>} 角色一致性检查结果
     */
    async checkCharacterConsistency(newImageUrl, characterName) {
        const reference = this.getCharacterReference(characterName);
        
        if (!reference) {
            // 如果没有参考图像，将当前图像设为参考
            this.setCharacterReference(characterName, newImageUrl, {
                isFirstAppearance: true
            });
            
            return {
                isConsistent: true,
                score: 1.0,
                isFirstAppearance: true,
                message: `首次出现，已设为参考图像`
            };
        }

        // 简化的一致性检查（在实际应用中可以集成更复杂的图像比较算法）
        const consistencyScore = await this.calculateImageSimilarity(reference.url, newImageUrl);
        const isConsistent = consistencyScore >= this.consistencyThreshold;

        return {
            isConsistent,
            score: consistencyScore,
            referenceUrl: reference.url,
            threshold: this.consistencyThreshold,
            message: isConsistent ? '一致性检查通过' : '一致性不足，建议重新生成'
        };
    }

    /**
     * 计算图像相似度（简化版本）
     * @param {string} imageUrl1 - 第一张图像URL
     * @param {string} imageUrl2 - 第二张图像URL
     * @returns {Promise<number>} 相似度分数 (0-1)
     */
    async calculateImageSimilarity(imageUrl1, imageUrl2) {
        try {
            // 这是一个简化的实现
            // 在实际应用中，可以使用更复杂的图像比较算法
            
            // 如果是同一张图像，返回1
            if (imageUrl1 === imageUrl2) {
                return 1.0;
            }

            // 简化的相似度计算（基于URL和时间戳的启发式方法）
            // 在实际应用中应该使用真正的图像分析
            const similarity = Math.random() * 0.3 + 0.7; // 模拟70%-100%的相似度
            
            return Math.min(1.0, Math.max(0.0, similarity));
        } catch (error) {
            console.error('图像相似度计算失败:', error);
            return 0.5; // 返回中等相似度作为默认值
        }
    }

    /**
     * 检查风格一致性
     * @param {string} imageUrl - 图像URL
     * @param {Object} expectedStyle - 期望的风格参数
     * @returns {Promise<Object>} 风格一致性检查结果
     */
    async checkStyleConsistency(imageUrl, expectedStyle) {
        // 简化的风格一致性检查
        const styleChecks = {
            artStyle: true,
            colorPalette: true,
            composition: true,
            mood: true
        };

        const issues = [];
        const suggestions = [];

        // 在实际应用中，这里会进行真正的风格分析
        // 目前使用简化的检查逻辑
        
        const overallConsistency = Object.values(styleChecks).every(check => check);

        return {
            isConsistent: overallConsistency,
            styleChecks,
            issues,
            suggestions,
            expectedStyle
        };
    }

    /**
     * 记录质量指标
     * @param {string} imageUrl - 图像URL
     * @param {Object} consistencyResult - 一致性检查结果
     */
    recordQualityMetrics(imageUrl, consistencyResult) {
        const metrics = {
            timestamp: Date.now(),
            imageUrl,
            consistencyScore: consistencyResult.score,
            isConsistent: consistencyResult.isConsistent,
            characterCount: Object.keys(consistencyResult.characterChecks).length
        };

        this.qualityMetrics.push(metrics);
        
        // 保持最近100条记录
        if (this.qualityMetrics.length > 100) {
            this.qualityMetrics = this.qualityMetrics.slice(-100);
        }
    }

    /**
     * 获取质量报告
     * @returns {Object} 质量报告
     */
    getQualityReport() {
        if (this.qualityMetrics.length === 0) {
            return {
                totalImages: 0,
                averageConsistency: 0,
                consistentImages: 0,
                consistencyRate: 0
            };
        }

        const totalImages = this.qualityMetrics.length;
        const consistentImages = this.qualityMetrics.filter(m => m.isConsistent).length;
        const averageConsistency = this.qualityMetrics.reduce((sum, m) => sum + m.consistencyScore, 0) / totalImages;
        const consistencyRate = consistentImages / totalImages;

        return {
            totalImages,
            averageConsistency: Math.round(averageConsistency * 100) / 100,
            consistentImages,
            consistencyRate: Math.round(consistencyRate * 100) / 100,
            recentMetrics: this.qualityMetrics.slice(-10) // 最近10条记录
        };
    }

    /**
     * 重置一致性检查器
     */
    reset() {
        this.referenceImages.clear();
        this.qualityMetrics = [];
        console.log('一致性检查器已重置');
    }

    /**
     * 设置一致性阈值
     * @param {number} threshold - 新的阈值 (0-1)
     */
    setConsistencyThreshold(threshold) {
        if (threshold >= 0 && threshold <= 1) {
            this.consistencyThreshold = threshold;
            console.log(`一致性阈值已设置为: ${threshold}`);
        } else {
            console.error('阈值必须在0-1之间');
        }
    }

    /**
     * 获取所有参考图像信息
     * @returns {Object} 参考图像信息
     */
    getAllReferences() {
        const references = {};
        for (const [characterName, imageInfo] of this.referenceImages) {
            references[characterName] = imageInfo;
        }
        return references;
    }
}

export default ConsistencyChecker;
