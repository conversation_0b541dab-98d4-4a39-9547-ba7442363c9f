/**
 * 测试插画生成改进
 * 验证OpenAI角色描述和事件提取功能
 */

import storyData from './src/data/storyData.js';
import { 
  generateIllustrationFromAnswer,
  extractUserEvents,
  identifyMentionedCharacters,
  getCharacterDescriptions,
  buildPromptFromKeywords
} from './src/services/illustrationGenerator.js';

// 测试用例
const testCases = [
  {
    pageId: 4,
    userAnswer: "我会对小兔子说'你好，我叫波波'，然后问她'你的歌声真好听，你在做什么呢？'",
    description: "第4页交互 - 打招呼场景"
  },
  {
    pageId: 8,
    userAnswer: "我会先走向莉莉，因为她是我的朋友，然后请她介绍其他朋友给我认识",
    description: "第8页交互 - 融入朋友圈"
  },
  {
    pageId: 11,
    userAnswer: "友谊让波波变得勇敢和快乐。我和朋友一起帮助过一只受伤的小鸟，我们一起找到了它的巢穴",
    description: "第11页交互 - 友谊的变化"
  }
];

async function testIllustrationImprovements() {
  console.log('🧪 开始测试插画生成改进...\n');

  // 1. 测试角色描述提取
  console.log('📋 测试1: 角色描述提取');
  const characterDescriptions = getCharacterDescriptions(storyData);
  console.log('角色描述:', characterDescriptions);
  console.log('✅ 角色描述提取完成\n');

  // 2. 测试每个用例
  for (const testCase of testCases) {
    console.log(`🎭 测试用例: ${testCase.description}`);
    console.log(`📝 用户回答: "${testCase.userAnswer}"`);

    // 提取用户事件
    const userEvents = extractUserEvents(testCase.userAnswer);
    console.log(`🎬 提取的事件: ${userEvents.join(', ')}`);

    // 识别提到的角色
    const mentionedCharacters = identifyMentionedCharacters(testCase.userAnswer, characterDescriptions);
    console.log(`👥 识别的角色: ${mentionedCharacters.map(c => c.name).join(', ')}`);

    // 检查OpenAI关键词
    const storyPage = storyData.pages.find(p => p.id === testCase.pageId);
    if (storyPage && storyPage.image_keywords) {
      console.log('✅ 找到OpenAI图像关键词');
      console.log('🔑 关键词预览:', {
        characters: storyPage.image_keywords.characters.slice(0, 2),
        scene: storyPage.image_keywords.scene,
        mood: storyPage.image_keywords.mood
      });

      // 测试基于关键词的提示词构建
      const prompt = buildPromptFromKeywords(
        storyPage.image_keywords,
        testCase.userAnswer,
        testCase.pageId,
        characterDescriptions
      );
      console.log('🎨 生成的提示词长度:', prompt.length, '字符');
      console.log('📋 提示词预览:', prompt.substring(0, 200) + '...');
    } else {
      console.log('⚠️ 未找到OpenAI关键词');
    }

    console.log('---\n');
  }

  console.log('🎉 测试完成！');
  
  // 输出改进总结
  console.log('\n📊 改进总结:');
  console.log('1. ✅ 添加了OpenAI生成的角色详细描述');
  console.log('2. ✅ 为所有页面添加了image_keywords');
  console.log('3. ✅ 实现了用户事件提取功能');
  console.log('4. ✅ 实现了角色识别功能');
  console.log('5. ✅ 优化了提示词构建逻辑');
  console.log('6. ✅ 确保事件描述准确反映在插画中');
}

// 运行测试
testIllustrationImprovements().catch(console.error);
