import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import i18nService, { Language } from '../services/i18nService';
import speechService, { VoiceOption } from '../services/speechService';

interface LanguageSettingsProps {
  onClose?: () => void;
}

export function LanguageSettings({ onClose }: LanguageSettingsProps) {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(i18nService.getCurrentLanguage());
  const [availableVoices, setAvailableVoices] = useState<VoiceOption[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [isTestingVoice, setIsTestingVoice] = useState(false);

  useEffect(() => {
    // 监听语言变化
    const handleLanguageChange = (language: Language) => {
      setCurrentLanguage(language);
      loadVoices();
    };

    i18nService.addLanguageChangeListener(handleLanguageChange);
    
    // 初始化语音
    initializeVoices();

    return () => {
      i18nService.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);

  const initializeVoices = async () => {
    await speechService.waitForInitialization();
    loadVoices();
  };

  const loadVoices = () => {
    const voices = speechService.getAvailableVoices();
    setAvailableVoices(voices);
    
    // 设置当前选中的语音
    const savedVoiceName = localStorage.getItem('storybook-voice');
    if (savedVoiceName) {
      setSelectedVoice(savedVoiceName);
    } else if (voices.length > 0) {
      setSelectedVoice(voices[0].name);
    }
  };

  const handleLanguageChange = (language: Language) => {
    i18nService.setLanguage(language);
    speechService.setLanguage(language);
  };

  const handleVoiceChange = (voiceName: string) => {
    setSelectedVoice(voiceName);
    const voice = availableVoices.find(v => v.name === voiceName)?.voice;
    if (voice) {
      speechService.setVoice(voice);
    }
  };

  const testVoice = () => {
    if (isTestingVoice) return;
    
    setIsTestingVoice(true);
    const testText = currentLanguage === 'zh' 
      ? '你好！我是小女孩语音，很高兴为你朗读故事。'
      : 'Hello! I am a little girl voice, happy to read stories for you.';
    
    speechService.speak(testText, {
      onEnd: () => setIsTestingVoice(false),
      onError: () => setIsTestingVoice(false)
    });
  };

  const getVoiceTypeIcon = (voice: VoiceOption) => {
    if (voice.isChildVoice) return '👧';
    if (voice.gender === 'female') return '👩';
    if (voice.gender === 'male') return '👨';
    return '🎤';
  };

  const getVoiceTypeText = (voice: VoiceOption) => {
    if (voice.isChildVoice) return currentLanguage === 'zh' ? '儿童声音' : 'Child Voice';
    if (voice.gender === 'female') return currentLanguage === 'zh' ? '女声' : 'Female';
    if (voice.gender === 'male') return currentLanguage === 'zh' ? '男声' : 'Male';
    return currentLanguage === 'zh' ? '未知' : 'Unknown';
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>⚙️ {i18nService.t('settings')}</span>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 语言设置 */}
        <div className="space-y-3">
          <h3 className="font-medium flex items-center gap-2">
            🌍 {i18nService.t('language')}
          </h3>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={currentLanguage === 'zh' ? 'default' : 'outline'}
              onClick={() => handleLanguageChange('zh')}
              className="flex items-center gap-2"
            >
              🇨🇳 中文
            </Button>
            <Button
              variant={currentLanguage === 'en' ? 'default' : 'outline'}
              onClick={() => handleLanguageChange('en')}
              className="flex items-center gap-2"
            >
              🇺🇸 English
            </Button>
          </div>
        </div>

        {/* 语音设置 */}
        <div className="space-y-3">
          <h3 className="font-medium flex items-center gap-2">
            🎤 {i18nService.t('voiceSettings')}
          </h3>
          
          {availableVoices.length > 0 ? (
            <div className="space-y-3">
              <Select value={selectedVoice} onValueChange={handleVoiceChange}>
                <SelectTrigger>
                  <SelectValue placeholder={i18nService.t('selectVoice')} />
                </SelectTrigger>
                <SelectContent>
                  {availableVoices.map((voice) => (
                    <SelectItem key={voice.name} value={voice.name}>
                      <div className="flex items-center gap-2">
                        <span>{getVoiceTypeIcon(voice)}</span>
                        <span className="truncate">{voice.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {getVoiceTypeText(voice)}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* 语音测试 */}
              <Button
                onClick={testVoice}
                disabled={isTestingVoice || !selectedVoice}
                variant="outline"
                className="w-full"
              >
                {isTestingVoice ? (
                  <>
                    🔊 {currentLanguage === 'zh' ? '测试中...' : 'Testing...'}
                  </>
                ) : (
                  <>
                    🔊 {currentLanguage === 'zh' ? '测试语音' : 'Test Voice'}
                  </>
                )}
              </Button>

              {/* 推荐语音提示 */}
              {availableVoices.some(v => v.isChildVoice || v.gender === 'female') && (
                <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                  💡 {currentLanguage === 'zh' 
                    ? '推荐使用小女孩或女声语音，更适合儿童故事朗读。'
                    : 'Recommended to use little girl or female voice, more suitable for children\'s story reading.'
                  }
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm text-gray-500 text-center py-4">
              {currentLanguage === 'zh' 
                ? '正在加载语音选项...'
                : 'Loading voice options...'
              }
            </div>
          )}
        </div>

        {/* 语音说明 */}
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          <p className="mb-2">
            {currentLanguage === 'zh' 
              ? '💡 语音功能说明：'
              : '💡 Voice Feature Description:'
            }
          </p>
          <ul className="space-y-1 list-disc list-inside">
            <li>
              {currentLanguage === 'zh' 
                ? '自动选择最适合的小女孩音色'
                : 'Automatically selects the most suitable little girl voice'
              }
            </li>
            <li>
              {currentLanguage === 'zh' 
                ? '支持中英文语音合成和识别'
                : 'Supports Chinese and English speech synthesis and recognition'
              }
            </li>
            <li>
              {currentLanguage === 'zh' 
                ? '语音设置会自动保存'
                : 'Voice settings are automatically saved'
              }
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

export default LanguageSettings;
