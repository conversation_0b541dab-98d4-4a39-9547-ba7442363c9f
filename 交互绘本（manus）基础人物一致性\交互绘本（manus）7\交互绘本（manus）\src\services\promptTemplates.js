// 为自闭症儿童语音交互绘本设计的提示词模板

// 故事生成提示词模板
const STORY_PROMPT_TEMPLATE = `
请为{age_range}岁的自闭症儿童创作一个以"{theme}"为主题的12页绘本故事。

要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在故事中随机安排3个交互环节（第4页、第8页和第11页），这些页面需要设计问题
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "title": "故事标题",
  "characters": [
    {
      "name": "角色名称",
      "description": "详细的角色外貌描述，包括：物种/种族、毛色/肤色、身高体型、服装颜色和款式、面部特征、特殊标识等，至少50字",
      "personality": "性格特点和行为习惯",
      "appearance_details": {
        "species": "物种或种族",
        "color": "主要颜色（毛色/肤色）",
        "clothing": "服装详细描述",
        "features": "特殊特征或标识",
        "size": "体型大小描述"
      }
    }
  ],
  "pages": [
    {
      "page_number": 1,
      "content": "第1页内容...",
      "is_interactive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "儿童插画风格"
      }
    },
    {
      "page_number": 4,
      "content": "第4页内容...",
      "is_interactive": true,
      "interactive_question": "问题内容...",
      "guidance_prompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "儿童插画风格"
      }
    }
    // 其他页面...
  ]
}
`;

// 针对不同主题的专门提示词模板
const THEME_SPECIFIC_PROMPTS = {
  "人际关系": `
请为{age_range}岁的自闭症儿童创作一个关于"人际关系"的12页绘本故事。

故事主题要求：
- 重点展现如何与他人建立友谊
- 包含分享、合作、互助的情节
- 展示如何处理与朋友的小冲突
- 教导基本的社交礼仪和沟通技巧
- 强调倾听和理解他人感受的重要性

角色设定建议：
- 主角：一个善良但有些内向的小动物
- 配角：不同性格的朋友们
- 情境：学校、游乐场、家庭聚会等社交场所

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：社交启动技能（如何开始交流、克服社交焦虑）
   - 第8页：群体融入技能（理解社交情境、适应群体动态）
   - 第11页：反思与泛化技能（总结经验、应用到生活中）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合AI图像生成的详细英文关键词，严格按照以下格式：
- characters: 角色的详细外貌描述（英文），包括：物种、毛色/肤色、服装颜色和款式、表情、姿态等
- scene: 具体场景环境（英文），如：sunny forest clearing, cozy home interior, bright classroom等
- mood: 情绪氛围词汇（英文），如：warm and friendly, joyful and excited, peaceful and calm等
- actions: 具体动作描述（英文），如：walking together, sharing toys, reading books等
- objects: 场景中的物品（英文），如：colorful flowers, wooden toys, picture books等
- lighting: 光照效果（英文），如：soft natural lighting, warm golden hour, bright daylight等
- style: 固定使用"children's book illustration, watercolor style, soft pastel colors"

注意：所有关键词必须使用英文，要具体详细，适合Stable Diffusion等AI图像生成模型

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "人际关系",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["white rabbit with blue eyes, wearing yellow apron, happy expression", "brown bear cub, red shirt, friendly smile"],
        "scene": "sunny forest clearing with tall trees",
        "mood": "warm and friendly, joyful atmosphere",
        "actions": ["walking on forest path", "exploring nature"],
        "objects": ["green grass", "colorful wildflowers", "tree branches"],
        "lighting": "soft natural lighting, dappled sunlight",
        "style": "children's book illustration, watercolor style, soft pastel colors"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["white rabbit with blue eyes, wearing yellow apron, excited expression", "brown bear cub, red shirt, helping gesture"],
        "scene": "bright classroom with wooden desks",
        "mood": "encouraging and supportive, learning atmosphere",
        "actions": ["raising hand to answer", "sharing with friends"],
        "objects": ["colorful books", "wooden chairs", "blackboard"],
        "lighting": "bright classroom lighting, warm indoor glow",
        "style": "children's book illustration, watercolor style, soft pastel colors"
      }
    }
    // 其他页面...
  ]
}
`,

  "家庭生活": `
请为{age_range}岁的自闭症儿童创作一个关于"家庭生活"的12页绘本故事。

故事主题要求：
- 展现温馨的家庭日常生活
- 包含家庭成员之间的关爱和支持
- 教导家庭责任和基本的家务参与
- 展示如何表达对家人的爱和感谢
- 处理家庭中的小问题和情感交流

角色设定建议：
- 主角：一个可爱的小动物孩子
- 配角：爸爸、妈妈、兄弟姐妹、爷爷奶奶等
- 情境：家中的客厅、厨房、卧室、花园等

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和家庭互动场景
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：家庭责任认知（理解家庭角色、培养责任感）
   - 第8页：情感表达技能（学会表达爱意、处理家庭冲突）
   - 第11页：家庭价值观内化（理解家庭重要性、感恩表达）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合图像生成的关键词，包括：
- characters: 角色的具体外貌描述（发色、服装、表情等）
- scene: 场景环境描述（室内/户外、具体地点）
- mood: 情绪氛围（温馨、和谐、快乐等）
- actions: 具体动作描述（拥抱、做饭、游戏等）
- objects: 相关物品（家具、玩具、食物等）
- style: 统一使用"温暖儿童插画风格，柔和色彩"

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "家庭生活",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    }
    // 其他页面...
  ]
}
`,

  "法律常识": `
请为{age_range}岁的自闭症儿童创作一个关于"法律常识"的12页绘本故事。

故事主题要求：
- 用简单易懂的方式介绍基本的法律概念
- 教导什么是对的行为，什么是错的行为
- 包含保护自己和他人的基本规则
- 展示遵守规则的重要性
- 教导如何在遇到问题时寻求帮助

角色设定建议：
- 主角：一个聪明好学的小动物
- 配角：警察叔叔、老师、家长等权威人物
- 情境：学校、公园、商店、马路等公共场所

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的规则教育和安全意识
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：规则理解认知（理解基本规则、安全意识培养）
   - 第8页：行为判断技能（区分对错行为、后果分析）
   - 第11页：规则应用能力（在生活中应用规则、寻求帮助）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合图像生成的关键词，包括：
- characters: 角色的具体外貌描述（发色、服装、表情等）
- scene: 场景环境描述（室内/户外、具体地点）
- mood: 情绪氛围（严肃、教育性、安全等）
- actions: 具体动作描述（学习、遵守、保护等）
- objects: 相关物品（标志、规则牌、安全设备等）
- style: 统一使用"温暖儿童插画风格，柔和色彩"

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "法律常识",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    }
    // 其他页面...
  ]
}
`,

  "人伦道德": `
请为{age_range}岁的自闭症儿童创作一个关于"人伦道德"的12页绘本故事。

故事主题要求：
- 教导基本的道德品质：诚实、善良、勇敢、感恩
- 展现如何尊重长辈和关爱他人
- 包含助人为乐和无私奉献的情节
- 教导如何做出正确的道德选择
- 强调同情心和责任感的培养

角色设定建议：
- 主角：一个有爱心的小动物
- 配角：需要帮助的朋友、长辈、陌生人等
- 情境：社区、学校、家庭等各种需要道德选择的场景

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的道德教育和品格培养
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：道德认知启蒙（理解善恶概念、培养同理心）
   - 第8页：道德选择技能（面临道德冲突时的决策能力）
   - 第11页：道德行为实践（将道德理念转化为具体行动）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合图像生成的关键词，包括：
- characters: 角色的具体外貌描述（发色、服装、表情等）
- scene: 场景环境描述（室内/户外、具体地点）
- mood: 情绪氛围（善良、温暖、正义等）
- actions: 具体动作描述（帮助、关爱、分享等）
- objects: 相关物品（礼物、工具、需要帮助的物品等）
- style: 统一使用"温暖儿童插画风格，柔和色彩"

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "人伦道德",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    }
    // 其他页面...
  ]
}
`
};

// ===== 绘本插画一致性关键词模板 =====

// 基础风格固定词组 - 每张图都必须包含的核心风格描述
const BASE_STYLE_KEYWORDS = `children's book illustration, soft watercolor style, warm pastel colors, gentle lighting, simple clean lines, minimalist background, picture book art, storybook illustration, hand-drawn aesthetic`;

// 动态人物设定模板 - 根据OpenAI描述实时生成
const CHARACTER_TEMPLATE_STRUCTURE = {
  // 人物基础信息参数
  basic_info: {
    age_group: ['young child', 'little girl', 'little boy', 'young person'],
    gender: ['girl', 'boy', 'child'],
    species: ['human', 'bear', 'rabbit', 'cat', 'dog', 'other animal']
  },

  // 外貌特征参数
  appearance: {
    hair_color: ['brown', 'black', 'blonde', 'red', 'gray', 'white'],
    hair_style: ['curly', 'straight', 'wavy', 'short', 'long', 'in ponytails', 'in braids', 'in a bun'],
    eye_color: ['bright green', 'bright blue', 'warm brown', 'dark brown', 'hazel'],
    skin_tone: ['fair', 'medium', 'dark', 'warm', 'cool'],
    body_type: ['short stature', 'tall', 'medium height', 'small', 'large']
  },

  // 服装参数
  clothing: {
    top: ['cardigan', 't-shirt', 'sweater', 'dress', 'shirt', 'jacket'],
    top_color: ['red', 'blue', 'green', 'yellow', 'pink', 'white', 'black', 'purple'],
    bottom: ['overalls', 'pants', 'shorts', 'skirt', 'jeans'],
    bottom_color: ['blue', 'black', 'brown', 'green', 'red', 'white'],
    accessories: ['collar with bell', 'hat', 'glasses', 'bow', 'necklace', 'backpack']
  },

  // 表情和姿态参数
  expression: {
    facial_expression: ['friendly smile', 'cheerful expression', 'gentle smile', 'curious look', 'peaceful expression', 'bright smile'],
    posture: ['standing', 'sitting', 'walking', 'running', 'playing', 'reading'],
    personality_traits: ['friendly', 'cheerful', 'gentle', 'curious', 'brave', 'kind', 'playful']
  }
};

// 默认人物模板（作为回退选项）
const DEFAULT_CHARACTER_TEMPLATES = {
  main_character: `a young child with friendly smile, cheerful expression`,
  animal_companion: `a small friendly animal with gentle expression`,
  supporting_character: `a kind person with warm smile, gentle expression`
};

// 颜色调色板固定
const COLOR_PALETTE = `warm pastel colors, soft blues, gentle greens, warm yellows, muted pinks, cream whites, light browns`;

// 固定艺术风格描述
const ART_STYLE_DESCRIPTION = `watercolor painting, soft brush strokes, paper texture, children's book art, gentle shading, minimal details, clean composition`;

// 光照一致性
const LIGHTING_CONSISTENCY = `soft natural lighting, warm golden hour light, gentle shadows, diffused sunlight`;

// 视角保持
const PERSPECTIVE_CONSISTENCY = `eye-level perspective, medium shot, centered composition, child-friendly viewpoint`;

// 情绪表达变化词汇
const EMOTION_KEYWORDS = {
  happy: `bright smile, sparkling eyes, cheerful expression, uplifted posture, warm atmosphere`,
  curious: `wide curious eyes, slightly tilted head, questioning expression, exploratory pose`,
  peaceful: `gentle smile, peaceful expression, relaxed posture, calm atmosphere`,
  surprised: `wide eyes, open mouth, surprised expression, animated gesture`
};

// 图片生成提示词模板（优化版 - 使用关键词结构）
const IMAGE_PROMPT_TEMPLATE = `{base_style} {character_description} {action_description} {scene_description} {emotion_keywords} {lighting} {art_style}`;

// 传统中文描述模板（备用）
const IMAGE_PROMPT_TEMPLATE_CHINESE = `
为自闭症儿童绘本创作一张插图，描述以下场景：

"{scene_description}"

要求：
1. 使用明亮、柔和的色彩
2. 简洁清晰的线条和形状
3. 避免过于复杂或混乱的背景
4. 角色表情要明确、易于识别
5. 图像应具有温暖、友好的氛围
6. 适合{age_range}岁自闭症儿童的视觉感知特点
7. 风格应保持一致，类似儿童插画书
8. 避免使用过多的文字或抽象符号

图像应该能够直观地表达场景内容，帮助自闭症儿童理解故事情节。
`;

// 专为LiblibAI image2image功能设计的提示词模板（优化版）
const IMAGE2IMAGE_PROMPT_TEMPLATE = `
Create a cheerful children's storybook illustration using the reference image style:

Scene: {scene_description}

Style Match:
- Same artistic style as reference
- Same watercolor technique
- Same color palette
- Same line style
- Same character design
- Same lighting style

Content Update:
- Update scene based on: {user_answer}
- Add new elements in same style
- Show happy character expressions
- Age-appropriate for {age_range} years old
- Family-friendly content

Create a wholesome children's book illustration that matches the reference style perfectly.
`;

// 交互式插画生成提示词模板（优化版 - 使用关键词结构）
const INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE = `{base_style} {character_description} {action_based_on_answer} {scene_description} {emotion_keywords} {lighting} {art_style}`;

// 交互式插画生成提示词模板（中文备用版）
const INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE_CHINESE = `
为自闭症儿童绘本创建插图。

用户回答: "{user_answer}"

{character_descriptions}
场景: {scene_context}
具体事件: {user_events}
情绪氛围: 友好、教育性
物品: {scene_objects}
光线: 柔和自然光
风格: 温暖儿童插画风格，柔和色彩

重要要求:
1. 准确反映用户回答中的具体事件和动作
2. 确保所有角色的外貌与OpenAI生成的描述完全一致
3. 场景要生动展现用户回答中的情境
4. 保持与绘本其他页面的风格一致性
5. 插画应该看起来像是同一位艺术家创作的同一本书的一部分
`;

// ===== 通用交互问题生成系统 =====

// 第一层：基础观察问题（适用于任何页面）
const UNIVERSAL_OBSERVATION_QUESTIONS = {
  general_observation: {
    question: "请仔细看看这幅图，告诉我你看到了什么？请尽可能详细地描述你看到的人物、动物、物品和场景。",
    guidance_prompt: "你可以从角色开始说起，比如'我看到了...'，然后再说说他们在做什么，周围有什么东西。"
  },
  emotion_recognition: {
    question: "看看图片中的角色，你觉得他们现在的心情是什么样的？你是从哪些地方看出来的？比如他们的表情、动作或者姿态。",
    guidance_prompt: "你可以看看他们的脸部表情，是笑着的还是其他表情？他们的身体姿势看起来怎么样？"
  },
  scene_understanding: {
    question: "这个场景发生在什么地方？你觉得这里是什么样的环境？请描述一下你看到的背景和周围的事物。",
    guidance_prompt: "你可以说说这是在室内还是室外？有什么特别的东西吗？比如树木、房子、玩具等等。"
  }
};

// 第二层：主题引导问题（基于AI识别的主题）
const THEME_GUIDED_QUESTIONS = {
  interpersonal_relationships: [
    {
      question: "如果图片中有多个角色，你觉得他们之间是什么关系？他们在一起做什么？",
      guidance_prompt: "你可以想想他们是朋友吗？还是家人？他们看起来在一起玩耍还是在聊天？"
    },
    {
      question: "如果你是图片中的角色，你会对其他角色说什么话？为什么？",
      guidance_prompt: "你可以想象自己就在图片里，你会想和他们说'你好'吗？还是想说其他的话？"
    },
    {
      question: "你觉得这些角色接下来会怎么互动？请描述你想象的场景。",
      guidance_prompt: "你可以想象一下，他们接下来可能会一起做什么有趣的事情？"
    }
  ],
  family_life: [
    {
      question: "看看这个场景，你觉得这像不像你熟悉的日常生活？哪些地方很像？",
      guidance_prompt: "你可以想想你在家里或者其他地方有没有见过类似的情况？"
    },
    {
      question: "如果这个场景发生在你家里，你会怎么参与？请详细说说你会做什么。",
      guidance_prompt: "你可以想象这件事发生在你家，你会想加入他们吗？你会做什么？"
    },
    {
      question: "你觉得这个家庭场景给你什么感觉？温暖、热闹还是安静？为什么？",
      guidance_prompt: "你可以说说这个场景让你感觉怎么样？开心吗？舒服吗？"
    }
  ],
  rules_and_safety: [
    {
      question: "在这个场景中，你觉得有什么规则需要遵守？为什么要有这些规则？",
      guidance_prompt: "你可以想想在这种地方，大家需要注意什么？比如要排队、要小心等等。"
    },
    {
      question: "如果图片中的角色做了不对的事，你会怎么提醒他们？",
      guidance_prompt: "你可以想想如果有人做了不安全或者不礼貌的事，你会怎么友好地告诉他们？"
    },
    {
      question: "你觉得这个场景安全吗？如果不安全，应该怎么做才能更安全？",
      guidance_prompt: "你可以看看有没有什么地方需要小心的？怎么做会更安全？"
    }
  ],
  moral_character: [
    {
      question: "你觉得图片中的角色表现出了什么好的品质？比如善良、勇敢、诚实等。",
      guidance_prompt: "你可以想想他们做的事情是不是很善良？很勇敢？还是很诚实？"
    },
    {
      question: "如果你是图片中的角色，你会怎么做才能表现得更好？",
      guidance_prompt: "你可以想想如果是你，你会怎么做？会更加友好吗？会帮助别人吗？"
    },
    {
      question: "这个场景让你想到了什么好的行为？请说说你在生活中类似的经历。",
      guidance_prompt: "你可以想想你有没有做过类似的好事？或者见过别人做好事？"
    }
  ]
};

// 第三层：创造性表达问题（促进描述性回答）
const CREATIVE_EXPRESSION_QUESTIONS = [
  {
    question: "请你想象一下，这个故事接下来会发生什么？请详细描述你想象的场景，包括角色会做什么、说什么、去哪里。",
    guidance_prompt: "你可以发挥想象力，想想接下来会有什么有趣的事情发生？"
  },
  {
    question: "如果你是图片中的主角，在这种情况下你会怎么做？请告诉我你的想法、感受和行动计划。",
    guidance_prompt: "你可以想象自己就是主角，你会有什么感觉？会想做什么？"
  },
  {
    question: "你想在这个场景中添加什么新的元素吗？比如新的角色、物品或者活动？请详细描述你的想法。",
    guidance_prompt: "你可以想想如果在这个场景里加上一些新的东西，会变得怎么样？"
  }
];

// 传统交互问题生成模板（备用）
const INTERACTIVE_QUESTION_PROMPT_TEMPLATE = `
为{age_range}岁自闭症儿童设计一个关于"{context}"的交互问题。

故事背景：
"{story_context}"

要求：
1. 问题应促进语言表达，而非简单的是/否回答
2. 问题应与故事情节和主题"{theme}"相关
3. 问题应考虑自闭症儿童的认知特点
4. 问题应鼓励分享个人经历或想法
5. 问题应有明确的焦点，避免模糊或抽象
6. 同时设计一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "question": "问题内容...",
  "guidance_prompt": "引导提示内容..."
}
`;

// 增强版自闭症儿童性能评估提示词模板（包含完整上下文）
const AUTISM_PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE = `
你是一位专业的自闭症儿童康复专家和语言治疗师，请分析以下6-8岁自闭症儿童在交互式绘本中的回答表现。

## 项目背景介绍
这是一个专门为自闭症儿童设计的AI驱动交互式绘本系统，旨在通过个性化的故事体验和智能分析，帮助评估和提升自闭症儿童的语言交流、社交认知、逻辑推理、情感调节和自我反思能力。

## 绘本故事信息
- 故事标题：{story_title}
- 绘本主题：{story_theme}
- 目标年龄：{age_group}自闭症儿童
- 故事概要：{story_summary}
- 主要角色：{main_characters}
- 故事背景：{story_setting}

## 交互设计理念
本绘本采用渐进式交互设计，每个问题都经过精心设计，考虑了自闭症儿童的认知特点：
1. 问题具体明确，避免抽象概念
2. 提供视觉化的情境描述
3. 鼓励个人经历分享和情感表达
4. 循序渐进地培养社交技能

## 交互问题与儿童回答

问题1（社交启动技能）："{question1}"
儿童回答1："{answer1}"

问题2（群体融入技能）："{question2}"
儿童回答2："{answer2}"

问题3（友谊理解与反思）："{question3}"
儿童回答3："{answer3}"

请从以下专业维度进行深度分析（满分5分）：

1. 语言表达能力（Language Expression）
   - 词汇使用的丰富度和准确性
   - 句式结构的复杂程度
   - 语言组织的逻辑性
   - 表达的清晰度和完整性

2. 社交认知能力（Social Cognition）
   - 对社交情境的理解程度
   - 他人情感和意图的识别能力
   - 社交规则和礼仪的掌握
   - 换位思考和共情能力

3. 逻辑推理能力（Logical Reasoning）
   - 因果关系的理解和表达
   - 问题解决的策略性思维
   - 序列性思维和步骤规划
   - 抽象概念的理解能力

4. 情感调节能力（Emotional Regulation）
   - 情感词汇的使用和理解
   - 情绪状态的识别和表达
   - 应对策略的提出和运用
   - 情感共鸣和情绪管理

5. 自我反思能力（Self-Reflection）
   - 对自身行为的觉察
   - 从经验中学习的能力
   - 自我改进的意识
   - 个人经历的整合表达

请特别关注自闭症儿童的特殊需求：
- 感觉处理差异
- 社交沟通挑战
- 重复行为和特殊兴趣
- 变化适应困难
- 非语言沟通理解

输出格式（严格JSON格式）：
{
  "assessment_summary": {
    "child_age_estimate": "基于回答推测的发展水平",
    "overall_performance": "整体表现概述",
    "key_strengths": ["优势1", "优势2", "优势3"],
    "areas_for_improvement": ["需改进领域1", "需改进领域2", "需改进领域3"]
  },
  "question_by_question_analysis": {
    "question_1": {
      "question_content": "{question1}",
      "child_response": "{answer1}",
      "analysis": "对这个具体回答的详细分析，包括语言表达、逻辑思维、社交理解等方面的表现",
      "identified_issues": ["具体问题1", "具体问题2"],
      "positive_aspects": ["积极表现1", "积极表现2"],
      "improvement_suggestions": ["针对性改进建议1", "针对性改进建议2"],
      "related_skills": ["涉及的技能领域1", "涉及的技能领域2"]
    },
    "question_2": {
      "question_content": "{question2}",
      "child_response": "{answer2}",
      "analysis": "对这个具体回答的详细分析，包括语言表达、逻辑思维、社交理解等方面的表现",
      "identified_issues": ["具体问题1", "具体问题2"],
      "positive_aspects": ["积极表现1", "积极表现2"],
      "improvement_suggestions": ["针对性改进建议1", "针对性改进建议2"],
      "related_skills": ["涉及的技能领域1", "涉及的技能领域2"]
    },
    "question_3": {
      "question_content": "{question3}",
      "child_response": "{answer3}",
      "analysis": "对这个具体回答的详细分析，包括语言表达、逻辑思维、社交理解等方面的表现",
      "identified_issues": ["具体问题1", "具体问题2"],
      "positive_aspects": ["积极表现1", "积极表现2"],
      "improvement_suggestions": ["针对性改进建议1", "针对性改进建议2"],
      "related_skills": ["涉及的技能领域1", "涉及的技能领域2"]
    }
  },
  "detailed_scores": {
    "language_expression": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "social_cognition": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "logical_reasoning": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "emotional_regulation": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "self_reflection": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    }
  },
  "intervention_plan": {
    "immediate_goals": ["短期目标1", "短期目标2", "短期目标3"],
    "long_term_goals": ["长期目标1", "长期目标2"],
    "recommended_activities": [
      {
        "activity": "活动名称",
        "description": "活动描述",
        "target_skills": ["目标技能1", "目标技能2"],
        "frequency": "建议频率"
      }
    ],
    "parent_guidance": ["家长指导建议1", "家长指导建议2", "家长指导建议3"],
    "progress_indicators": ["进步指标1", "进步指标2"]
  },
  "professional_notes": {
    "autism_specific_observations": "自闭症特征相关观察",
    "communication_style": "沟通风格分析",
    "learning_preferences": "学习偏好分析",
    "sensory_considerations": "感觉处理相关建议"
  }
}
`;

// 保持原有的简单版本作为备用
const PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE = `
分析自闭症儿童在以下交互问题中的回答表现：

问题1："{question1}"
回答1："{answer1}"

问题2："{question2}"
回答2："{answer2}"

问题3："{question3}"
回答3："{answer3}"

请从以下四个维度进行评估（满分5分）：
1. 语言词汇量：评估词汇丰富度、表达多样性
2. 思维逻辑：评估因果关系理解、逻辑推理能力
3. 社会适应：评估社交规则理解、人际互动意识
4. 情感识别：评估情感表达、共情能力

对于每个维度，请提供具体分析和改进建议。

输出格式：
{
  "scores": {
    "language_vocabulary": 分数,
    "logical_thinking": 分数,
    "social_adaptation": 分数,
    "emotional_recognition": 分数
  },
  "analysis": {
    "language_vocabulary": "分析和建议...",
    "logical_thinking": "分析和建议...",
    "social_adaptation": "分析和建议...",
    "emotional_recognition": "分析和建议..."
  },
  "overall_recommendation": "综合建议..."
}
`;

// ===== 关键词构建函数 =====

/**
 * 根据OpenAI角色描述动态生成英文关键词
 * @param {Object} characterData - OpenAI提供的角色数据
 * @param {string} characterData.name - 角色名称
 * @param {string} characterData.description - 角色的中文描述
 * @returns {string} 英文关键词格式的角色描述
 */
function generateDynamicCharacterKeywords(characterData) {
  if (!characterData || !characterData.description) {
    return DEFAULT_CHARACTER_TEMPLATES.main_character;
  }

  const description = characterData.description.toLowerCase();
  let keywords = [];

  // 1. 基础信息识别
  if (description.includes('小女孩') || description.includes('女孩')) {
    keywords.push('a young girl');
  } else if (description.includes('小男孩') || description.includes('男孩')) {
    keywords.push('a young boy');
  } else if (description.includes('小熊')) {
    keywords.push('a cute little bear');
  } else if (description.includes('小兔') || description.includes('兔子')) {
    keywords.push('a small rabbit');
  } else if (description.includes('小猫') || description.includes('猫')) {
    keywords.push('a small cat');
  } else if (description.includes('小狗') || description.includes('狗')) {
    keywords.push('a small dog');
  } else {
    keywords.push('a young child');
  }

  // 2. 头发特征
  let hairFeatures = [];

  // 头发颜色
  if (description.includes('棕色') && description.includes('头发')) {
    hairFeatures.push('brown');
  } else if (description.includes('黑色') && description.includes('头发')) {
    hairFeatures.push('black');
  } else if (description.includes('金色') && description.includes('头发')) {
    hairFeatures.push('blonde');
  } else if (description.includes('红色') && description.includes('头发')) {
    hairFeatures.push('red');
  }

  // 头发质地
  if (description.includes('卷') && description.includes('头发')) {
    hairFeatures.push('curly');
  } else if (description.includes('直') && description.includes('头发')) {
    hairFeatures.push('straight');
  }

  // 发型
  if (description.includes('马尾')) {
    hairFeatures.push('hair in ponytails');
  } else if (description.includes('辫子')) {
    hairFeatures.push('hair in braids');
  } else if (description.includes('短发')) {
    hairFeatures.push('short hair');
  } else if (description.includes('长发')) {
    hairFeatures.push('long hair');
  } else {
    hairFeatures.push('hair');
  }

  if (hairFeatures.length > 0) {
    keywords.push(`with ${hairFeatures.join(' ')}`);
  }

  // 3. 服装识别
  let clothingItems = [];

  // 上衣
  if (description.includes('红色') && (description.includes('上衣') || description.includes('开衫'))) {
    clothingItems.push('wearing a red cardigan');
  } else if (description.includes('蓝色') && description.includes('上衣')) {
    clothingItems.push('wearing a blue shirt');
  } else if (description.includes('绿色') && description.includes('上衣')) {
    clothingItems.push('wearing a green top');
  } else if (description.includes('黄色') && description.includes('上衣')) {
    clothingItems.push('wearing a yellow shirt');
  } else if (description.includes('粉色') && description.includes('连衣裙')) {
    clothingItems.push('wearing a pink dress');
  }

  // 下装
  if (description.includes('蓝色') && (description.includes('裤子') || description.includes('背带裤'))) {
    clothingItems.push('and blue overalls');
  } else if (description.includes('短裤')) {
    clothingItems.push('and shorts');
  } else if (description.includes('裙子')) {
    clothingItems.push('and a skirt');
  }

  if (clothingItems.length > 0) {
    keywords.push(clothingItems.join(' '));
  }

  // 4. 眼睛颜色
  if (description.includes('绿色') && description.includes('眼睛')) {
    keywords.push('bright green eyes');
  } else if (description.includes('蓝色') && description.includes('眼睛')) {
    keywords.push('bright blue eyes');
  } else if (description.includes('棕色') && description.includes('眼睛')) {
    keywords.push('warm brown eyes');
  } else if (description.includes('黑色') && description.includes('眼睛')) {
    keywords.push('dark brown eyes');
  }

  // 5. 体型特征
  if (description.includes('小') || description.includes('矮')) {
    keywords.push('short stature');
  } else if (description.includes('高')) {
    keywords.push('tall');
  }

  // 6. 表情特征
  if (description.includes('友好') || description.includes('友善')) {
    keywords.push('friendly smile');
  } else if (description.includes('温和') || description.includes('温柔')) {
    keywords.push('gentle expression');
  } else if (description.includes('开心') || description.includes('快乐')) {
    keywords.push('cheerful expression');
  } else {
    keywords.push('friendly smile');
  }

  return keywords.join(', ');
}

/**
 * 构建标准插画关键词（动态版本）
 * @param {Object} options - 配置选项
 * @param {Object} options.characterData - OpenAI角色数据 {name, description}
 * @param {string} options.action - 动作描述
 * @param {string} options.scene - 场景描述
 * @param {string} options.emotion - 情绪类型 ('happy', 'curious', 'peaceful', 'surprised')
 * @param {string} options.customCharacter - 自定义角色描述（可选，优先级最高）
 * @returns {string} 完整的关键词提示
 */
function buildStandardIllustrationKeywords(options = {}) {
  const {
    characterData = null,
    action = 'standing',
    scene = 'in a garden',
    emotion = 'happy',
    customCharacter = null
  } = options;

  // 优先级：自定义描述 > 动态生成 > 默认模板
  let characterDescription;
  if (customCharacter) {
    characterDescription = customCharacter;
  } else if (characterData) {
    characterDescription = generateDynamicCharacterKeywords(characterData);
  } else {
    characterDescription = DEFAULT_CHARACTER_TEMPLATES.main_character;
  }

  // 构建完整关键词
  const keywords = [
    BASE_STYLE_KEYWORDS,
    characterDescription,
    action,
    scene,
    EMOTION_KEYWORDS[emotion] || EMOTION_KEYWORDS.happy,
    LIGHTING_CONSISTENCY,
    ART_STYLE_DESCRIPTION
  ].join(', ');

  return keywords;
}

/**
 * 构建交互式插画关键词（动态版本）
 * @param {Object} options - 配置选项
 * @param {string} options.userAnswer - 用户回答
 * @param {Object} options.characterData - OpenAI角色数据 {name, description}
 * @param {string} options.scene - 场景描述
 * @param {string} options.emotion - 情绪类型
 * @param {string} options.customCharacter - 自定义角色描述（可选，优先级最高）
 * @returns {string} 完整的关键词提示
 */
function buildInteractiveIllustrationKeywords(options = {}) {
  const {
    userAnswer = '',
    characterData = null,
    scene = 'in a garden',
    emotion = 'happy',
    customCharacter = null
  } = options;

  // 从用户回答中提取动作
  const actionBasedOnAnswer = extractActionFromAnswer(userAnswer);

  // 优先级：自定义描述 > 动态生成 > 默认模板
  let characterDescription;
  if (customCharacter) {
    characterDescription = customCharacter;
  } else if (characterData) {
    characterDescription = generateDynamicCharacterKeywords(characterData);
  } else {
    characterDescription = DEFAULT_CHARACTER_TEMPLATES.main_character;
  }

  // 构建完整关键词
  const keywords = [
    BASE_STYLE_KEYWORDS,
    characterDescription,
    actionBasedOnAnswer,
    scene,
    EMOTION_KEYWORDS[emotion] || EMOTION_KEYWORDS.happy,
    LIGHTING_CONSISTENCY,
    ART_STYLE_DESCRIPTION
  ].join(', ');

  return keywords;
}

// ===== 语音转图片关键词提取系统 =====

// 情感词汇库
const EMOTION_VOCABULARY = {
  positive: ["开心", "快乐", "高兴", "兴奋", "满意", "愉快", "开心", "喜欢", "爱", "棒", "好"],
  negative: ["难过", "伤心", "害怕", "担心", "生气", "失望", "不开心", "哭", "疼", "不好"],
  neutral: ["平静", "安静", "思考", "专注", "好奇", "看", "听", "想"]
};

// 动作词汇库
const ACTION_VOCABULARY = {
  movement: ["跑", "走", "跳", "爬", "飞", "游泳", "骑", "开车"],
  interaction: ["拥抱", "握手", "波手", "亲", "碰", "推", "拉", "给"],
  communication: ["说话", "聊天", "唱歌", "叫", "喊", "告诉", "问"],
  expression: ["笑", "哭", "叫", "喊", "微笑", "皱眉"],
  daily_activities: ["吃", "喝", "睡", "洗", "穿", "脱", "刷牙", "洗脸"],
  play_activities: ["玩", "游戏", "踢球", "画画", "读书", "唱歌", "跳舞"]
};

// 场景元素库
const SCENE_VOCABULARY = {
  indoor: ["房间", "客厅", "卧室", "厨房", "浴室", "教室", "办公室", "商店", "医院"],
  outdoor: ["公园", "花园", "操场", "街道", "森林", "海边", "山", "河", "湖"],
  objects: ["桌子", "椅子", "床", "沙发", "书", "玩具", "球", "车", "花", "树"],
  weather: ["晴天", "雨天", "雪", "风", "云", "太阳", "月亮", "星星"]
};

// 角色词汇库
const CHARACTER_VOCABULARY = {
  family: ["妈妈", "爸爸", "爷爷", "奶奶", "哥哥", "姐姐", "弟弟", "妹妹", "宝宝"],
  social: ["朋友", "同学", "老师", "医生", "警察", "司机", "售货员"],
  animals: ["小猫", "小狗", "小兔", "小鸟", "小鱼", "小熊", "小象", "小马"],
  general: ["小朋友", "大人", "人", "大家", "我", "你", "他", "她"]
};

/**
 * 从用户回答中提取情感关键词
 * @param {string} userAnswer - 用户回答
 * @returns {string} 情感关键词
 */
function extractEmotionKeywords(userAnswer) {
  if (!userAnswer) return 'happy, cheerful expression';

  const answer = userAnswer.toLowerCase();

  // 检查积极情感
  for (const emotion of EMOTION_VOCABULARY.positive) {
    if (answer.includes(emotion)) {
      return 'happy, bright smile, sparkling eyes, cheerful expression, joyful atmosphere';
    }
  }

  // 检查消极情感
  for (const emotion of EMOTION_VOCABULARY.negative) {
    if (answer.includes(emotion)) {
      return 'sad, concerned expression, gentle comfort, supportive atmosphere';
    }
  }

  // 检查中性情感
  for (const emotion of EMOTION_VOCABULARY.neutral) {
    if (answer.includes(emotion)) {
      return 'curious, thoughtful expression, peaceful atmosphere, gentle focus';
    }
  }

  return 'happy, friendly expression, warm atmosphere';
}

/**
 * 从用户回答中提取动作关键词
 * @param {string} userAnswer - 用户回答
 * @returns {string} 动作关键词
 */
function extractActionKeywords(userAnswer) {
  if (!userAnswer) return 'standing peacefully';

  const answer = userAnswer.toLowerCase();

  // 检查各类动作
  for (const [category, actions] of Object.entries(ACTION_VOCABULARY)) {
    for (const action of actions) {
      if (answer.includes(action)) {
        switch (category) {
          case 'movement':
            if (action === '跑') return 'running joyfully';
            if (action === '走') return 'walking calmly';
            if (action === '跳') return 'jumping happily';
            return 'moving actively';
          case 'interaction':
            if (action === '拥抱') return 'hugging warmly';
            if (action === '握手') return 'shaking hands friendly';
            return 'interacting kindly';
          case 'communication':
            if (action === '说话') return 'talking cheerfully';
            if (action === '唱歌') return 'singing happily';
            return 'communicating expressively';
          case 'expression':
            if (action === '笑') return 'laughing joyfully';
            if (action === '微笑') return 'smiling gently';
            return 'expressing emotions';
          case 'play_activities':
            if (action === '玩') return 'playing happily';
            if (action === '画画') return 'drawing creatively';
            return 'engaging in fun activities';
          default:
            return 'engaging in activity';
        }
      }
    }
  }

  return 'standing peacefully';
}

/**
 * 从用户回答中提取场景关键词
 * @param {string} userAnswer - 用户回答
 * @returns {string} 场景关键词
 */
function extractSceneKeywords(userAnswer) {
  if (!userAnswer) return 'in a beautiful garden with flowers and trees';

  const answer = userAnswer.toLowerCase();

  // 检查室内场景
  for (const scene of SCENE_VOCABULARY.indoor) {
    if (answer.includes(scene)) {
      if (scene === '教室') return 'in a bright classroom with books and desks';
      if (scene === '房间' || scene === '客厅') return 'in a cozy home interior with warm lighting';
      if (scene === '商店') return 'in a friendly store with colorful items';
      return 'in a comfortable indoor space';
    }
  }

  // 检查户外场景
  for (const scene of SCENE_VOCABULARY.outdoor) {
    if (answer.includes(scene)) {
      if (scene === '公园') return 'in a sunny park with playground equipment';
      if (scene === '花园') return 'in a beautiful garden with blooming flowers';
      if (scene === '森林') return 'in a magical forest with tall trees and sunlight';
      return 'in a lovely outdoor setting';
    }
  }

  return 'in a beautiful garden with flowers and trees';
}

/**
 * 从用户回答中提取角色关键词
 * @param {string} userAnswer - 用户回答
 * @param {Object} characterData - 主要角色数据
 * @returns {string} 角色关键词
 */
function extractCharacterKeywords(userAnswer, characterData) {
  // 优先使用动态生成的角色描述
  if (characterData) {
    return generateDynamicCharacterKeywords(characterData);
  }

  if (!userAnswer) return DEFAULT_CHARACTER_TEMPLATES.main_character;

  const answer = userAnswer.toLowerCase();

  // 检查提到的角色类型
  for (const [category, characters] of Object.entries(CHARACTER_VOCABULARY)) {
    for (const character of characters) {
      if (answer.includes(character)) {
        if (category === 'animals') {
          if (character === '小兔') return 'a cute little rabbit with long ears, fluffy tail, gentle expression';
          if (character === '小熊') return 'a friendly little bear with warm brown fur, round face, kind eyes';
          if (character === '小猫') return 'a small cat with soft fur, bright eyes, playful expression';
          return 'a friendly animal character with gentle expression';
        } else {
          return DEFAULT_CHARACTER_TEMPLATES.main_character;
        }
      }
    }
  }

  return DEFAULT_CHARACTER_TEMPLATES.main_character;
}

/**
 * 智能关键词生成器（基于用户回答）
 * @param {string} userAnswer - 用户回答
 * @param {Object} characterData - 角色数据
 * @param {string} originalScene - 原始场景
 * @returns {string} 完整的关键词提示
 */
function generateSmartKeywords(userAnswer, characterData, originalScene = null) {
  console.log('🧠 智能关键词生成 - 用户回答:', userAnswer);

  const extractedElements = {
    emotions: extractEmotionKeywords(userAnswer),
    actions: extractActionKeywords(userAnswer),
    characters: extractCharacterKeywords(userAnswer, characterData),
    scenes: originalScene || extractSceneKeywords(userAnswer)
  };

  console.log('📊 提取的元素:', extractedElements);

  // 构建完整关键词
  const keywords = [
    BASE_STYLE_KEYWORDS,
    extractedElements.characters,
    extractedElements.actions,
    extractedElements.scenes,
    extractedElements.emotions,
    LIGHTING_CONSISTENCY,
    ART_STYLE_DESCRIPTION
  ].join(', ');

  console.log('🎯 生成的智能关键词:', keywords);
  return keywords;
}

/**
 * 从用户回答中提取动作描述（保持向后兼容）
 * @param {string} userAnswer - 用户回答
 * @returns {string} 动作描述
 */
function extractActionFromAnswer(userAnswer) {
  return extractActionKeywords(userAnswer);
}

// ===== 智能问题选择算法 =====

/**
 * 页面内容分析器
 * @param {Object} pageData - 页面数据
 * @param {string} pageContent - 页面内容
 * @returns {Object} 分析结果
 */
function analyzePageContent(pageData, pageContent) {
  const analysis = {
    characterCount: 0,
    sceneType: 'unknown',
    emotionalTone: 'neutral',
    actionState: 'static',
    themeCategory: 'general'
  };

  const content = (pageContent || '').toLowerCase();

  // 分析角色数量
  const characterKeywords = ['角色', '人物', '朋友', '妈妈', '爸爸', '老师', '小朋友', '大家', '他们'];
  characterKeywords.forEach(keyword => {
    if (content.includes(keyword)) {
      analysis.characterCount = content.includes('他们') || content.includes('大家') ? 2 : 1;
    }
  });

  // 分析场景类型
  if (content.includes('家') || content.includes('房间') || content.includes('客厅')) {
    analysis.sceneType = 'home';
  } else if (content.includes('学校') || content.includes('教室') || content.includes('老师')) {
    analysis.sceneType = 'school';
  } else if (content.includes('公园') || content.includes('户外') || content.includes('花园')) {
    analysis.sceneType = 'outdoor';
  } else if (content.includes('商店') || content.includes('超市') || content.includes('公共')) {
    analysis.sceneType = 'public';
  }

  // 分析情感基调
  const positiveWords = ['开心', '快乐', '高兴', '兴奋', '满意', '愉快', '笑'];
  const negativeWords = ['难过', '伤心', '害怕', '担心', '生气', '失望'];

  if (positiveWords.some(word => content.includes(word))) {
    analysis.emotionalTone = 'positive';
  } else if (negativeWords.some(word => content.includes(word))) {
    analysis.emotionalTone = 'negative';
  }

  // 分析动作状态
  const actionWords = ['跑', '走', '跳', '玩', '说话', '拥抱', '握手', '互动'];
  if (actionWords.some(word => content.includes(word))) {
    analysis.actionState = 'active';
  }

  // 判断主题类别
  if (analysis.characterCount > 1 && analysis.actionState === 'active') {
    analysis.themeCategory = 'interpersonal_relationships';
  } else if (analysis.sceneType === 'home') {
    analysis.themeCategory = 'family_life';
  } else if (analysis.sceneType === 'public' || content.includes('规则') || content.includes('安全')) {
    analysis.themeCategory = 'rules_and_safety';
  } else {
    analysis.themeCategory = 'moral_character';
  }

  return analysis;
}

/**
 * 智能选择交互问题
 * @param {Object} pageData - 页面数据
 * @param {string} pageContent - 页面内容
 * @param {number} questionIndex - 问题索引（0-2）
 * @returns {Object} 选中的问题
 */
function selectInteractiveQuestion(pageData, pageContent, questionIndex = 0) {
  const analysis = analyzePageContent(pageData, pageContent);

  console.log('📊 页面内容分析结果:', analysis);

  // 标准三问组合策略
  if (questionIndex === 0) {
    // 第一个问题：基础观察问题
    const observationKeys = Object.keys(UNIVERSAL_OBSERVATION_QUESTIONS);
    const selectedKey = observationKeys[Math.floor(Math.random() * observationKeys.length)];
    return {
      ...UNIVERSAL_OBSERVATION_QUESTIONS[selectedKey],
      type: 'observation',
      analysis: analysis
    };
  } else if (questionIndex === 1) {
    // 第二个问题：主题相关问题
    const themeQuestions = THEME_GUIDED_QUESTIONS[analysis.themeCategory] || THEME_GUIDED_QUESTIONS.moral_character;
    const selectedQuestion = themeQuestions[Math.floor(Math.random() * themeQuestions.length)];
    return {
      ...selectedQuestion,
      type: 'theme_guided',
      theme: analysis.themeCategory,
      analysis: analysis
    };
  } else {
    // 第三个问题：创造性表达问题
    const selectedQuestion = CREATIVE_EXPRESSION_QUESTIONS[Math.floor(Math.random() * CREATIVE_EXPRESSION_QUESTIONS.length)];
    return {
      ...selectedQuestion,
      type: 'creative_expression',
      analysis: analysis
    };
  }
}

/**
 * 生成完整的交互问题集合
 * @param {Object} pageData - 页面数据
 * @param {string} pageContent - 页面内容
 * @returns {Array} 三个问题的数组
 */
function generateInteractiveQuestionSet(pageData, pageContent) {
  const questions = [];

  for (let i = 0; i < 3; i++) {
    const question = selectInteractiveQuestion(pageData, pageContent, i);
    questions.push({
      id: i + 1,
      ...question,
      pageAnalysis: question.analysis
    });
  }

  console.log('🎯 生成的交互问题集合:', questions);
  return questions;
}

export {
  STORY_PROMPT_TEMPLATE,
  THEME_SPECIFIC_PROMPTS,
  IMAGE_PROMPT_TEMPLATE,
  IMAGE_PROMPT_TEMPLATE_CHINESE,
  IMAGE2IMAGE_PROMPT_TEMPLATE,
  INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE,
  INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE_CHINESE,
  INTERACTIVE_QUESTION_PROMPT_TEMPLATE,
  PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE,
  AUTISM_PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE,
  // 新增的关键词相关导出
  BASE_STYLE_KEYWORDS,
  CHARACTER_TEMPLATE_STRUCTURE,
  DEFAULT_CHARACTER_TEMPLATES,
  COLOR_PALETTE,
  ART_STYLE_DESCRIPTION,
  LIGHTING_CONSISTENCY,
  PERSPECTIVE_CONSISTENCY,
  EMOTION_KEYWORDS,
  // 动态关键词生成函数
  generateDynamicCharacterKeywords,
  buildStandardIllustrationKeywords,
  buildInteractiveIllustrationKeywords,
  extractActionFromAnswer,
  // 新增的通用交互问题生成系统
  UNIVERSAL_OBSERVATION_QUESTIONS,
  THEME_GUIDED_QUESTIONS,
  CREATIVE_EXPRESSION_QUESTIONS,
  analyzePageContent,
  selectInteractiveQuestion,
  generateInteractiveQuestionSet,
  // 新增的语音转图片关键词提取系统
  EMOTION_VOCABULARY,
  ACTION_VOCABULARY,
  SCENE_VOCABULARY,
  CHARACTER_VOCABULARY,
  extractEmotionKeywords,
  extractActionKeywords,
  extractSceneKeywords,
  extractCharacterKeywords,
  generateSmartKeywords
};
