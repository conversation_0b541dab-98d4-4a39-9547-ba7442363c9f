// 故事生成器组件
// 提供主题选择和故事生成功能

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { Spinner } from './ui/spinner';
import storyGeneratorService from '../services/storyGeneratorService';
import storyIllustrationGenerator from '../services/storyIllustrationGenerator';
import i18nService, { Language } from '../services/i18nService';

interface StoryGeneratorProps {
  onStoryGenerated: (storyData: any) => void;
  onCancel: () => void;
}

export function StoryGenerator({ onStoryGenerated, onCancel }: StoryGeneratorProps) {
  const [selectedTheme, setSelectedTheme] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);
  const [error, setError] = useState<string>('');
  const [generationStep, setGenerationStep] = useState<string>('');
  const [imageProgress, setImageProgress] = useState<any>(null);
  const [currentLanguage, setCurrentLanguage] = useState<Language>(i18nService.getCurrentLanguage());

  const localizedThemes = storyGeneratorService.getLocalizedThemes();
  const isServiceAvailable = storyGeneratorService.isServiceAvailable();
  const isImageServiceAvailable = storyIllustrationGenerator.isServiceAvailable();

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = (language: Language) => {
      setCurrentLanguage(language);
    };

    i18nService.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18nService.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);

  const handleThemeSelect = (themeKey: string) => {
    setSelectedTheme(themeKey);
    setError('');
  };

  const handleGenerateStory = async () => {
    if (!selectedTheme) {
      setError(i18nService.t('pleaseSelectTheme'));
      return;
    }

    if (!isServiceAvailable) {
      setError(i18nService.t('openaiNotConfigured'));
      return;
    }

    setIsGenerating(true);
    setError('');
    setGenerationStep(currentLanguage === 'zh' ? '正在生成故事内容...' : 'Generating story content...');

    try {
      console.log(`🎨 开始生成主题为"${selectedTheme}"的故事...`);

      const storyData = await storyGeneratorService.generateStory(selectedTheme);

      console.log('✅ 故事生成成功:', storyData.title);
      setGenerationStep(currentLanguage === 'zh' ? '故事内容生成完成' : 'Story content generation completed');

      // 检查是否可以生成插图
      if (isImageServiceAvailable) {
        setIsGeneratingImages(true);
        setGenerationStep(currentLanguage === 'zh' ? '正在生成故事插图...' : 'Generating story illustrations...');

        try {
          console.log('🖼️ 开始生成故事插图...');

          const generatedImages = await storyIllustrationGenerator.generateStoryIllustrations(
            storyData,
            (progress) => {
              setImageProgress(progress);
              const progressMessage = currentLanguage === 'zh'
                ? `正在生成第${progress.currentPage}页插图... (${progress.current}/${progress.total})`
                : `Generating page ${progress.currentPage} illustration... (${progress.current}/${progress.total})`;
              setGenerationStep(progressMessage);
            }
          );

          // 将生成的插图URL更新到故事数据中
          const updatedStoryData = {
            ...storyData,
            pages: storyData.pages.map(page => {
              if (generatedImages[page.id]) {
                return {
                  ...page,
                  imagePath: generatedImages[page.id],
                  image: generatedImages[page.id]
                };
              }
              return page;
            })
          };

          console.log('🎉 故事和插图生成完成!');
          onStoryGenerated(updatedStoryData);

        } catch (imageError) {
          console.warn('⚠️ 插图生成失败，但故事内容已生成:', imageError);
          // 即使插图生成失败，也返回故事内容
          onStoryGenerated(storyData);
        }
      } else {
        console.log('⚠️ LiblibAI服务未初始化，跳过插图生成');
        onStoryGenerated(storyData);
      }

    } catch (err: any) {
      console.error('❌ 故事生成失败:', err);
      const errorMessage = currentLanguage === 'zh'
        ? (err.message || '故事生成失败，请重试')
        : (err.message || 'Story generation failed, please try again');
      setError(errorMessage);
    } finally {
      setIsGenerating(false);
      setIsGeneratingImages(false);
      setGenerationStep('');
      setImageProgress(null);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
      <Card className="max-w-2xl w-full">
        <CardHeader>
          <CardTitle className="text-3xl text-center">
            🎨 {i18nService.t('aiStoryGenerator')}
          </CardTitle>
          <p className="text-center text-gray-600">
            {i18nService.t('personalizedStoryDescription')}
          </p>
        </CardHeader>
        <CardContent>
          {/* API密钥状态提示 */}
          {!isServiceAvailable && (
            <Alert className="mb-6">
              <AlertDescription>
                <p>{i18nService.t('openaiConfigInstructions')}</p>
                <code className="block mt-2 p-2 bg-gray-100 rounded text-sm">
                  VITE_OPENAI_API_KEY=your_openai_api_key_here
                </code>
                <p className="mt-2 text-sm text-gray-600">
                  {i18nService.t('restartServerInstructions')}
                </p>
              </AlertDescription>
            </Alert>
          )}

          {/* 主题选择 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">{i18nService.t('selectStoryTheme')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {localizedThemes.map((theme) => (
                <Card
                  key={theme.key}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTheme === theme.key
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleThemeSelect(theme.key)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-lg">{theme.label}</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {storyGeneratorService.getThemeDescription(theme.key)}
                        </p>
                      </div>
                      {selectedTheme === theme.key && (
                        <Badge variant="default">{i18nService.t('selected')}</Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-center gap-4">
            <Button
              onClick={handleGenerateStory}
              disabled={isGenerating || !selectedTheme || !isServiceAvailable}
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Spinner className="mr-2" />
                  {isGeneratingImages ? i18nService.t('generatingImages') : i18nService.t('generatingStory')}
                </>
              ) : (
                `🎨 ${i18nService.t('generateStory')}`
              )}
            </Button>

            <Button
              onClick={onCancel}
              variant="outline"
              size="lg"
              disabled={isGenerating}
            >
              {i18nService.t('back')}
            </Button>
          </div>

          {/* 生成进度提示 */}
          {isGenerating && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="text-center">
                <p className="text-blue-800 font-medium">{generationStep || i18nService.t('storyGenerationInProgress')}</p>
                <p className="text-blue-600 text-sm mt-1">
                  {currentLanguage === 'zh'
                    ? `AI正在为您创作一个关于"${selectedTheme}"的精彩故事，请稍候`
                    : `AI is creating an exciting story about "${selectedTheme}" for you, please wait`
                  }
                </p>

                {/* 插图生成进度 */}
                {isGeneratingImages && imageProgress && (
                  <div className="mt-3">
                    <div className="flex justify-between text-xs text-blue-600 mb-1">
                      <span>{currentLanguage === 'zh' ? '插图生成进度' : 'Image Generation Progress'}</span>
                      <span>{imageProgress.current}/{imageProgress.total}</span>
                    </div>
                    <div className="w-full bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(imageProgress.current / imageProgress.total) * 100}%` }}
                      ></div>
                    </div>
                    {imageProgress.status === 'completed' && imageProgress.imageUrl && (
                      <p className="text-xs text-green-600 mt-1">
                        {currentLanguage === 'zh'
                          ? `✅ 第${imageProgress.currentPage}页插图生成完成`
                          : `✅ Page ${imageProgress.currentPage} image generation completed`
                        }
                      </p>
                    )}
                    {imageProgress.status === 'error' && (
                      <p className="text-xs text-red-600 mt-1">
                        {currentLanguage === 'zh'
                          ? `❌ 第${imageProgress.currentPage}页插图生成失败`
                          : `❌ Page ${imageProgress.currentPage} image generation failed`
                        }
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 功能说明 */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">{i18nService.t('features')}</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• {i18nService.t('autismFriendlyContent')}</li>
              <li>• {i18nService.t('fullStoryPages')}</li>
              <li>• {i18nService.t('simpleLanguage')}</li>
              <li>• {i18nService.t('clearEmotionalExpression')}</li>
              <li>• {i18nService.t('guidedInteraction')}</li>
              <li>• {i18nService.t('autoGeneratedImages')}</li>
            </ul>

            <h4 className="font-medium mb-2 mt-4">{i18nService.t('configurationNotes')}</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• {i18nService.t('secureApiKeys')}</li>
              <li>• {i18nService.t('noSensitiveInfo')}</li>
              <li>• {i18nService.t('environmentSupport')}</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
