import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// 导入增强服务到全局作用域以便测试
import liblibService from './services/liblibService.js'
import storyIllustrationGenerator from './services/storyIllustrationGenerator.js'
import illustrationGeneratorModule from './services/illustrationGenerator.js'

// 动态导入增强服务（避免循环依赖）
let enhancedIllustrationService: any = null;
let StyleManager: any = null;
let CharacterManager: any = null;
let SceneStyleController: any = null;
let ConsistencyChecker: any = null;
let EnhancedPromptManager: any = null;

// 异步加载增强服务
async function loadEnhancedServices() {
  try {
    const [
      enhancedIllustrationModule,
      styleManagerModule,
      characterManagerModule,
      sceneStyleModule,
      consistencyModule,
      promptManagerModule
    ] = await Promise.all([
      import('./services/enhancedIllustrationService.js'),
      import('./services/styleManager.js'),
      import('./services/characterManager.js'),
      import('./services/sceneStyleController.js'),
      import('./services/consistencyChecker.js'),
      import('./services/enhancedPromptManager.js')
    ]);

    enhancedIllustrationService = enhancedIllustrationModule.default;
    StyleManager = styleManagerModule.default;
    CharacterManager = characterManagerModule.default;
    SceneStyleController = sceneStyleModule.default;
    ConsistencyChecker = consistencyModule.default;
    EnhancedPromptManager = promptManagerModule.default;

    // 导出到全局作用域
    window.enhancedIllustrationService = enhancedIllustrationService;
    window.StyleManager = StyleManager;
    window.CharacterManager = CharacterManager;
    window.SceneStyleController = SceneStyleController;
    window.ConsistencyChecker = ConsistencyChecker;
    window.EnhancedPromptManager = EnhancedPromptManager;

    console.log('✅ 增强服务异步加载完成');
    return true;
  } catch (error) {
    console.error('❌ 增强服务加载失败:', error);
    return false;
  }
}

// 将服务导出到全局作用域
declare global {
  interface Window {
    liblibService: any;
    storyIllustrationGenerator: any;
    enhancedIllustrationService: any;
    illustrationGeneratorModule: any;
    generateIllustrationFromAnswer: any;
    extractKeyContent: any;
    StyleManager: any;
    CharacterManager: any;
    SceneStyleController: any;
    ConsistencyChecker: any;
    EnhancedPromptManager: any;
    loadEnhancedServices: () => Promise<boolean>;
  }
}

// 导出基础服务到全局作用域
window.liblibService = liblibService;
window.storyIllustrationGenerator = storyIllustrationGenerator;
window.illustrationGeneratorModule = illustrationGeneratorModule;
window.generateIllustrationFromAnswer = illustrationGeneratorModule.generateIllustrationFromAnswer;
window.extractKeyContent = illustrationGeneratorModule.extractKeyContent;
window.loadEnhancedServices = loadEnhancedServices;

console.log('🔧 基础服务已导出到全局作用域');
console.log('⏳ 正在异步加载增强服务...');

// 异步加载增强服务
loadEnhancedServices().then(success => {
  if (success) {
    console.log('🎉 所有服务已成功加载到全局作用域，可以进行测试');
  } else {
    console.log('⚠️ 部分增强服务加载失败，基础功能仍可用');
  }
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
