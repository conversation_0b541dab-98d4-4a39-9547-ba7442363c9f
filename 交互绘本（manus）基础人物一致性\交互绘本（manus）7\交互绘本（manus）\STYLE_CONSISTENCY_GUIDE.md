# 风格一致性策略使用指南

基于 `style control.txt` 文档实现的人物和风格一致性保证系统。

## 📋 概述

本系统实现了完整的风格一致性策略，确保交互绘本中的所有插画保持统一的艺术风格和角色特征。系统完全保留了您现有的 liblibai API 调用代码，只在提示词生成和一致性管理层面进行增强。

## 🏗️ 系统架构

### 核心模块

1. **StyleManager** (`src/services/styleManager.js`)
   - 管理基础风格模板
   - 生成风格一致的提示词
   - 处理情绪和场景特定的风格调整

2. **CharacterManager** (`src/services/characterManager.js`)
   - 维护角色特征库
   - 生成角色描述
   - 管理角色参考图像

3. **SceneStyleController** (`src/services/sceneStyleController.js`)
   - 统一管理场景风格
   - 协调角色和环境的组合
   - 生成场景特定的提示词

4. **ConsistencyChecker** (`src/services/consistencyChecker.js`)
   - 检查图像一致性
   - 维护质量指标
   - 提供一致性报告

5. **EnhancedPromptManager** (`src/services/enhancedPromptManager.js`)
   - 集成所有管理器
   - 生成增强的提示词
   - 管理提示词历史

6. **EnhancedIllustrationService** (`src/services/enhancedIllustrationService.js`)
   - 主要集成服务
   - 协调整个生成流程
   - 保持与现有 API 的兼容性

## 🚀 快速开始

### 1. 基础使用

```javascript
import enhancedIllustrationService from './src/services/enhancedIllustrationService.js';

// 设置故事上下文
const storyContext = {
  title: "小熊波波的友谊冒险",
  theme: "友谊",
  characters: ["小熊波波", "小兔子", "小松鼠"]
};

enhancedIllustrationService.setStoryContext(storyContext);

// 生成页面插画
const pageInfo = {
  pageNumber: 1,
  content: "小熊波波在森林中遇到了新朋友",
  sceneType: "forest",
  characters: [
    { name: "小熊波波", emotion: "curious", action: "exploring" },
    { name: "小兔子", emotion: "friendly", action: "greeting" }
  ],
  isInteractive: false
};

const imageUrl = await enhancedIllustrationService.generatePageIllustration(pageInfo);
```

### 2. 个性化交互插画

```javascript
// 基于用户回答生成个性化插画
const interactionInfo = {
  userAnswer: "我喜欢和朋友一起在森林里玩",
  questionContext: "你最喜欢和朋友做什么？",
  pageNumber: 4,
  baseCharacters: [
    { name: "小熊波波", emotion: "happy" },
    { name: "小兔子", emotion: "excited" }
  ],
  referenceImages: ["previous_page_image_url"]
};

const personalizedImageUrl = await enhancedIllustrationService.generatePersonalizedIllustration(interactionInfo);
```

## 🎨 风格控制特性

### 基础风格模板

系统使用专为自闭症儿童设计的风格模板：

- **艺术风格**: 儿童插画书风格
- **色彩方案**: 温暖柔和的粉彩色调
- **光照**: 柔和自然的光线
- **纹理**: 水彩画风格
- **构图**: 简单清晰的构图
- **情绪**: 友好温馨的氛围

### 情绪调整

系统支持基于情绪的风格调整：

```javascript
import StyleManager from './src/services/styleManager.js';

const styleManager = new StyleManager();

// 获取快乐情绪的风格调整
const happyStyle = styleManager.getEmotionStyleAdjustment("happy");
// 结果: { colorAdjustment: "bright, cheerful colors", ... }

// 获取害羞情绪的风格调整
const shyStyle = styleManager.getEmotionStyleAdjustment("shy");
// 结果: { colorAdjustment: "gentle, reassuring colors", ... }
```

### 场景特定风格

不同场景类型有专门的风格配置：

- **forest**: 森林场景 - 绿色和棕色色调
- **indoor**: 室内场景 - 温暖的室内色彩
- **playground**: 游乐场 - 明亮活泼的色彩
- **home**: 家庭场景 - 温馨熟悉的色彩
- **interaction**: 交互场景 - 柔和不分散注意力的色彩

## 👥 角色一致性

### 角色特征管理

系统维护详细的角色特征库：

```javascript
import CharacterManager from './src/services/characterManager.js';

const characterManager = new CharacterManager();

// 获取角色描述
const description = characterManager.getCharacterDescription(
  "小熊波波", 
  "happy", 
  "playing"
);

// 生成多角色场景描述
const multiCharDesc = characterManager.generateMultiCharacterDescription([
  { name: "小熊波波", emotion: "curious", action: "exploring" },
  { name: "小兔子", emotion: "friendly", action: "greeting" }
]);
```

### 种子图像生成

为主要角色生成种子图像以确保一致性：

```javascript
// 系统会自动为角色生成种子图像
// 种子图像用作后续生成的参考
const seedPrompt = characterManager.generateSeedImageDescription("小熊波波");
```

## 🔍 一致性检查

### 自动一致性验证

系统会自动检查生成图像的一致性：

```javascript
import ConsistencyChecker from './src/services/consistencyChecker.js';

const checker = new ConsistencyChecker();

// 检查图像一致性
const result = await checker.checkImageConsistency(
  newImageUrl, 
  ["小熊波波", "小兔子"]
);

console.log(result.isConsistent); // true/false
console.log(result.score); // 0.0 - 1.0
```

### 质量报告

获取详细的质量统计：

```javascript
const qualityReport = checker.getQualityReport();
console.log(qualityReport);
// {
//   totalImages: 10,
//   averageConsistency: 0.85,
//   consistentImages: 8,
//   consistencyRate: 0.8
// }
```

## 📊 监控和统计

### 生成统计

```javascript
const stats = enhancedIllustrationService.getGenerationStats();
console.log(stats);
// {
//   totalGenerated: 15,
//   personalizedCount: 5,
//   withReferenceCount: 12,
//   averageConsistency: 0.87,
//   characterReferencesCount: 3
// }
```

### 提示词历史

```javascript
import EnhancedPromptManager from './src/services/enhancedPromptManager.js';

const promptManager = new EnhancedPromptManager();

// 获取所有提示词历史
const history = promptManager.getPromptHistory();

// 获取特定类型的历史
const imageHistory = promptManager.getPromptHistory('image');
const personalizedHistory = promptManager.getPromptHistory('personalized');
```

## 🧪 测试和验证

### 运行测试

```javascript
import StyleConsistencyTest from './src/tests/styleConsistencyTest.js';

const test = new StyleConsistencyTest();
const results = await test.runAllTests();
```

### 浏览器中测试

在浏览器控制台中运行：

```javascript
// 运行完整测试套件
await window.runStyleConsistencyTest();
```

## ⚙️ 配置选项

### 一致性阈值调整

```javascript
import ConsistencyChecker from './src/services/consistencyChecker.js';

const checker = new ConsistencyChecker();

// 设置一致性阈值 (0.0 - 1.0)
checker.setConsistencyThreshold(0.8);
```

### 风格模板自定义

```javascript
import StyleManager from './src/services/styleManager.js';

const styleManager = new StyleManager();

// 可以通过修改 baseStylePrompt 来自定义风格
// 或者扩展 getEmotionStyleAdjustment 方法
```

## 🔧 与现有代码集成

### 无缝集成

系统设计为与现有代码无缝集成：

1. **保留现有 API 调用**: 所有 liblibai API 调用代码保持不变
2. **增强提示词**: 只在提示词生成层面进行增强
3. **向后兼容**: 现有功能完全保持兼容
4. **渐进式采用**: 可以逐步启用新功能

### 现有函数增强

原有的 `generateIllustrationFromAnswer` 函数已经增强：

```javascript
// 原有调用方式仍然有效
const imageUrl = await generateIllustrationFromAnswer(
  userAnswer, 
  pageId, 
  context, 
  allImages
);

// 现在会自动使用增强的一致性策略
// 如果增强功能失败，会自动回退到原始方法
```

## 📝 最佳实践

1. **设置故事上下文**: 在开始生成前设置完整的故事上下文
2. **使用角色种子**: 让系统为主要角色生成种子图像
3. **监控一致性**: 定期检查一致性统计和质量报告
4. **渐进式生成**: 按页面顺序生成，利用前页作为参考
5. **个性化适度**: 在保持一致性的前提下进行个性化

## 🚨 注意事项

1. **API 密钥**: 确保 liblibai API 密钥正确配置
2. **网络连接**: 图像生成需要稳定的网络连接
3. **浏览器兼容**: 某些功能需要现代浏览器支持
4. **存储空间**: 一致性检查会缓存参考图像
5. **性能考虑**: 首次生成角色种子图像可能需要额外时间

## 📞 支持

如果遇到问题或需要帮助：

1. 查看浏览器控制台的详细日志
2. 运行测试套件检查系统状态
3. 检查一致性统计和质量报告
4. 确认 API 密钥和网络连接正常

系统设计为高度可观测和可调试，所有关键操作都有详细的日志输出。
