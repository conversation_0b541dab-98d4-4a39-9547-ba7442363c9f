/**
 * 风格管理器 - 确保绘本风格一致性
 * 基于 style control.txt 文档的策略实现
 */

class StyleManager {
    constructor() {
        // 基础风格模板 - 专为自闭症儿童设计
        this.baseStylePrompt = {
            artStyle: "children's book illustration",
            colorPalette: "warm, soft pastel colors",
            lighting: "gentle, natural lighting", 
            texture: "watercolor painting style",
            composition: "simple, clear composition",
            mood: "friendly, welcoming atmosphere",
            detailLevel: "moderate detail, not overwhelming for autism children"
        };

        // 技术参数
        this.technicalParams = [
            "high quality",
            "masterpiece", 
            "professional illustration",
            "consistent style",
            "child-friendly",
            "autism-appropriate design",
            "no complex patterns",
            "clear emotions",
            "soft edges"
        ];

        // 负面提示词
        this.negativePrompt = [
            "dark",
            "scary", 
            "complex patterns",
            "overwhelming details",
            "realistic photography",
            "harsh lighting",
            "abstract art",
            "inconsistent style",
            "adult themes"
        ];
    }

    /**
     * 生成包含风格控制的完整提示词
     * @param {string} sceneDescription - 场景描述
     * @param {Object} options - 额外选项
     * @returns {Object} 包含prompt和negative_prompt的对象
     */
    generateStylePrompt(sceneDescription = "", options = {}) {
        const styleComponents = [
            this.baseStylePrompt.artStyle,
            this.baseStylePrompt.colorPalette,
            this.baseStylePrompt.lighting,
            this.baseStylePrompt.texture,
            this.baseStylePrompt.composition,
            this.baseStylePrompt.mood,
            this.baseStylePrompt.detailLevel
        ];

        // 构建基础提示词
        const basePrompt = sceneDescription ? 
            `${sceneDescription}, ${styleComponents.join(", ")}` :
            styleComponents.join(", ");

        // 添加技术参数
        const fullPrompt = `${basePrompt}, ${this.technicalParams.join(", ")}`;

        // 处理额外的负面提示词
        const fullNegativePrompt = options.additionalNegative ? 
            [...this.negativePrompt, ...options.additionalNegative].join(", ") :
            this.negativePrompt.join(", ");

        return {
            prompt: fullPrompt,
            negativePrompt: fullNegativePrompt,
            styleInfo: {
                artStyle: this.baseStylePrompt.artStyle,
                mood: this.baseStylePrompt.mood,
                colorPalette: this.baseStylePrompt.colorPalette
            }
        };
    }

    /**
     * 获取特定情绪的风格调整
     * @param {string} emotion - 情绪类型
     * @returns {Object} 风格调整参数
     */
    getEmotionStyleAdjustment(emotion) {
        const emotionStyles = {
            happy: {
                colorAdjustment: "bright, cheerful colors",
                lightingAdjustment: "warm, sunny lighting",
                moodAdjustment: "joyful, uplifting atmosphere"
            },
            sad: {
                colorAdjustment: "softer, muted colors",
                lightingAdjustment: "gentle, subdued lighting", 
                moodAdjustment: "gentle, comforting atmosphere"
            },
            excited: {
                colorAdjustment: "vibrant, energetic colors",
                lightingAdjustment: "bright, dynamic lighting",
                moodAdjustment: "energetic, enthusiastic atmosphere"
            },
            calm: {
                colorAdjustment: "peaceful, serene colors",
                lightingAdjustment: "soft, tranquil lighting",
                moodAdjustment: "peaceful, relaxing atmosphere"
            },
            curious: {
                colorAdjustment: "interesting, engaging colors",
                lightingAdjustment: "clear, focused lighting",
                moodAdjustment: "intriguing, discovery-focused atmosphere"
            },
            shy: {
                colorAdjustment: "gentle, reassuring colors",
                lightingAdjustment: "soft, non-threatening lighting",
                moodAdjustment: "safe, understanding atmosphere"
            }
        };

        return emotionStyles[emotion] || emotionStyles.calm;
    }

    /**
     * 为交互页面生成特殊风格
     * @param {string} interactionType - 交互类型
     * @returns {Object} 交互页面风格参数
     */
    getInteractivePageStyle(interactionType) {
        const interactiveStyles = {
            question: {
                background: "simple, non-distracting background",
                focus: "clear focus on characters and interaction",
                colors: "encouraging, warm colors",
                composition: "centered, balanced composition"
            },
            choice: {
                background: "neutral, calming background",
                focus: "clear visual hierarchy",
                colors: "distinct but harmonious colors",
                composition: "organized, easy-to-understand layout"
            },
            activity: {
                background: "engaging but not overwhelming background",
                focus: "activity-centered composition",
                colors: "motivating, positive colors",
                composition: "action-oriented, clear direction"
            }
        };

        return interactiveStyles[interactionType] || interactiveStyles.question;
    }

    /**
     * 获取场景特定的风格调整
     * @param {string} sceneType - 场景类型
     * @returns {Object} 场景风格参数
     */
    getSceneStyleAdjustment(sceneType) {
        const sceneStyles = {
            forest: {
                environment: "peaceful forest clearing",
                colors: "green and brown tones",
                elements: "trees, grass, flowers, soft sunlight"
            },
            indoor: {
                environment: "cozy indoor space",
                colors: "warm interior colors",
                elements: "furniture, books, toys, warm lighting"
            },
            playground: {
                environment: "safe playground area",
                colors: "bright, playful colors",
                elements: "playground equipment, grass, blue sky"
            },
            home: {
                environment: "comfortable home setting",
                colors: "homey, familiar colors",
                elements: "family furniture, personal items, cozy atmosphere"
            },
            interaction: {
                environment: "neutral background",
                colors: "soft, non-distracting colors",
                elements: "simple background, focus on characters"
            }
        };

        return sceneStyles[sceneType] || sceneStyles.forest;
    }

    /**
     * 生成场景特定的完整提示词
     * @param {string} sceneType - 场景类型
     * @param {string} characterDescription - 角色描述
     * @param {string} actionDescription - 动作描述
     * @returns {Object} 完整的场景提示词
     */
    generateScenePrompt(sceneType, characterDescription, actionDescription) {
        const sceneTemplate = this.getSceneStyleAdjustment(sceneType);

        const sceneDescription = [
            characterDescription,
            actionDescription,
            `in ${sceneTemplate.environment}`,
            sceneTemplate.colors,
            sceneTemplate.elements
        ].filter(Boolean).join(", ");

        return this.generateStylePrompt(sceneDescription);
    }
}

export default StyleManager;
