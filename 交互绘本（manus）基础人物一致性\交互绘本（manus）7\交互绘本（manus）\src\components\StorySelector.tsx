// 故事选择器组件
// 提供故事列表、选择和管理功能

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import storyManager from '../services/storyManager';
import i18nService, { Language } from '../services/i18nService';

interface StorySelectorProps {
  onStorySelected: (storyData: any) => void;
  onGenerateNew: () => void;
  onCancel: () => void;
}

export function StorySelector({ onStorySelected, onGenerateNew, onCancel }: StorySelectorProps) {
  const [stories, setStories] = useState<any[]>([]);
  const [selectedStoryId, setSelectedStoryId] = useState<string>('');
  const [stats, setStats] = useState<any>(null);
  const [currentLanguage, setCurrentLanguage] = useState<Language>(i18nService.getCurrentLanguage());

  useEffect(() => {
    loadStories();
  }, []);

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = (language: Language) => {
      setCurrentLanguage(language);
    };

    i18nService.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18nService.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);

  const loadStories = () => {
    const allStories = storyManager.getAllStories();
    const currentStory = storyManager.getCurrentStory();
    const storyStats = storyManager.getStoryStats();
    
    setStories(allStories);
    setSelectedStoryId(currentStory?.id || '');
    setStats(storyStats);
  };

  const handleStorySelect = (storyId: string) => {
    setSelectedStoryId(storyId);
  };

  const handleConfirmSelection = () => {
    if (selectedStoryId) {
      storyManager.switchToStory(selectedStoryId);
      const selectedStory = storyManager.getCurrentStory();
      onStorySelected(selectedStory);
    }
  };

  const handleDeleteStory = (storyId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (confirm('确定要删除这个故事吗？此操作不可撤销。')) {
      const success = storyManager.deleteStory(storyId);
      if (success) {
        loadStories(); // 重新加载故事列表
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getThemeColor = (theme: string) => {
    const colors = {
      '友谊': 'bg-blue-100 text-blue-800',
      '人际关系': 'bg-green-100 text-green-800',
      '家庭生活': 'bg-pink-100 text-pink-800',
      '法律常识': 'bg-purple-100 text-purple-800',
      '人伦道德': 'bg-orange-100 text-orange-800'
    };
    return colors[theme] || 'bg-gray-100 text-gray-800';
  };

  // 获取国际化的主题显示名称
  const getLocalizedTheme = (theme: string) => {
    const themeMap = {
      '友谊': currentLanguage === 'zh' ? '友谊' : 'Friendship',
      '人际关系': i18nService.t('themeInterpersonalRelations'),
      '家庭生活': i18nService.t('themeFamilyLife'),
      '法律常识': i18nService.t('themeLegalKnowledge'),
      '人伦道德': i18nService.t('themeMoralEthics')
    };
    return themeMap[theme] || theme;
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
      <Card className="max-w-4xl w-full">
        <CardHeader>
          <CardTitle className="text-3xl text-center">
            📚 {i18nService.t('storyLibrary')}
          </CardTitle>
          <p className="text-center text-gray-600">
            {i18nService.t('selectStoryDescription')}
          </p>
        </CardHeader>
        <CardContent>
          {/* 统计信息 */}
          {stats && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-2">{i18nService.t('storyLibraryStats')}</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">{i18nService.t('totalStories')}：</span>
                  <span className="font-medium">{stats.totalStories}</span>
                </div>
                <div>
                  <span className="text-gray-600">{i18nService.t('generatedStories')}：</span>
                  <span className="font-medium">{stats.generatedStories}</span>
                </div>
                <div>
                  <span className="text-gray-600">{currentLanguage === 'zh' ? '当前故事' : 'Current Story'}：</span>
                  <span className="font-medium">{stats.currentStory}</span>
                </div>
                <div>
                  <span className="text-gray-600">{currentLanguage === 'zh' ? '主题分布' : 'Theme Distribution'}：</span>
                  <span className="font-medium">
                    {Object.keys(stats.themeDistribution).length} {currentLanguage === 'zh' ? '种' : 'types'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 故事列表 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">{i18nService.t('selectStory')}</h3>
            {stories.length === 0 ? (
              <Alert>
                <AlertDescription>
                  {i18nService.t('noStoriesAvailable')}
                </AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {stories.map((story) => (
                  <Card 
                    key={story.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedStoryId === story.id 
                        ? 'ring-2 ring-blue-500 bg-blue-50' 
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleStorySelect(story.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-lg">{story.title}</h4>
                            {story.isDefault && (
                              <Badge variant="secondary" className="text-xs">
                                {i18nService.t('defaultStory')}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={`text-xs ${getThemeColor(story.theme)}`}>
                              {getLocalizedTheme(story.theme)}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {story.ageGroup}
                            </span>
                          </div>

                          <p className="text-sm text-gray-600 mb-2">
                            {story.pages?.length || 0} {i18nService.t('pages')} •
                            {story.pages?.filter((p: any) => p.isInteractive).length || 0} {i18nService.t('interactions')}
                          </p>
                          
                          <p className="text-xs text-gray-500">
                            {currentLanguage === 'zh' ? '创建于' : 'Created on'} {formatDate(story.createdAt)}
                          </p>
                        </div>

                        <div className="flex flex-col gap-1">
                          {selectedStoryId === story.id && (
                            <Badge variant="default" className="text-xs">
                              {i18nService.t('selected')}
                            </Badge>
                          )}
                          
                          {!story.isDefault && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto"
                              onClick={(e) => handleDeleteStory(story.id, e)}
                              title={currentLanguage === 'zh' ? '删除故事' : 'Delete Story'}
                            >
                              🗑️
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center gap-4">
            <Button
              onClick={handleConfirmSelection}
              disabled={!selectedStoryId}
              size="lg"
            >
              📖 {currentLanguage === 'zh' ? '开始阅读' : 'Start Reading'}
            </Button>

            <Button
              onClick={onGenerateNew}
              variant="outline"
              size="lg"
            >
              🎨 {i18nService.t('generateNewStory')}
            </Button>

            <Button
              onClick={onCancel}
              variant="ghost"
              size="lg"
            >
              {i18nService.t('back')}
            </Button>
          </div>

          {/* 功能说明 */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">
              {currentLanguage === 'zh' ? '使用说明' : 'Instructions'}
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• {currentLanguage === 'zh'
                ? '点击故事卡片选择要阅读的故事'
                : 'Click on a story card to select the story you want to read'
              }</li>
              <li>• {currentLanguage === 'zh'
                ? '默认故事是经过精心设计的《小熊波波的友谊冒险》'
                : 'The default story is the carefully designed "Bobo Bear\'s Friendship Adventure"'
              }</li>
              <li>• {currentLanguage === 'zh'
                ? '生成的故事会自动保存，可以随时切换阅读'
                : 'Generated stories are automatically saved and can be switched for reading at any time'
              }</li>
              <li>• {currentLanguage === 'zh'
                ? '可以删除不需要的生成故事（默认故事不可删除）'
                : 'You can delete unwanted generated stories (default stories cannot be deleted)'
              }</li>
              <li>• {currentLanguage === 'zh'
                ? '每个故事都包含12页内容和3个交互环节'
                : 'Each story contains 12 pages of content and 3 interactive sessions'
              }</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
