# AI故事生成功能使用说明

## 🎨 功能概述

本项目新增了基于OpenAI API的智能故事生成功能，可以为自闭症儿童生成针对不同主题的个性化绘本故事。

## 📚 支持的故事主题

### 1. 人际关系
- **主要内容**: 如何与他人建立友谊、分享合作、处理冲突
- **教育目标**: 培养社交技能和沟通能力
- **适用场景**: 学校、游乐场、社交聚会等

### 2. 家庭生活
- **主要内容**: 温馨的家庭日常、家庭责任、表达对家人的爱
- **教育目标**: 理解家庭关系，学习家庭责任
- **适用场景**: 家中各个房间、家庭活动等

### 3. 法律常识
- **主要内容**: 基本法律概念、行为规范、安全意识
- **教育目标**: 培养守法意识和自我保护能力
- **适用场景**: 公共场所、学校、社区等

### 4. 人伦道德
- **主要内容**: 诚实善良、尊重他人、助人为乐
- **教育目标**: 培养道德品质和同情心
- **适用场景**: 各种需要道德选择的生活情境

## 🚀 使用步骤

### 第一步：配置API密钥
1. 在项目根目录找到`.env`文件
2. 设置您的OpenAI API密钥：
   ```
   VITE_OPENAI_API_KEY=your_actual_openai_api_key_here
   ```
3. 保存文件

### 第二步：启动应用
```bash
npm run dev
```

### 第三步：进入故事生成界面
1. 打开浏览器访问应用
2. 在主界面点击 **"🎨 生成新故事"** 按钮

### 第四步：选择故事主题
1. 在主题选择界面，浏览四个可用主题
2. 点击您想要的主题卡片
3. 确认选择后点击 **"🎨 生成故事"** 按钮

### 第五步：等待故事生成
1. AI将根据选择的主题生成12页故事
2. 生成过程通常需要30-60秒
3. 生成完成后自动跳转到阅读界面

### 第六步：开始阅读
1. 新生成的故事会自动成为当前故事
2. 享受个性化的阅读体验
3. 参与3个交互环节

## 📖 故事管理功能

### 故事库
- 点击 **"📖 选择故事"** 可以查看所有已生成的故事
- 支持在不同故事之间切换
- 显示故事的主题、创建时间等信息

### 故事删除
- 可以删除不需要的生成故事
- 默认故事《小熊波波的友谊冒险》不可删除
- 删除操作不可撤销，请谨慎操作

### 数据持久化
- 所有生成的故事会自动保存到本地存储
- 重新打开应用时会自动加载之前的故事
- 支持多个故事的并行管理

## ⚙️ 技术特点

### AI生成质量保证
- 使用GPT-4o模型确保高质量内容
- 专门针对自闭症儿童的认知特点优化
- 严格的12页+3交互的结构要求

### 内容安全性
- 所有生成内容都经过安全过滤
- 适合儿童的语言和情节
- 避免复杂修辞和抽象概念

### 用户体验
- 直观的主题选择界面
- 实时生成进度提示
- 无缝的故事切换体验

## 🔧 故障排除

### API密钥问题
**问题**: 提示"OpenAI API密钥未配置"
**解决**:
1. 检查.env文件中的VITE_OPENAI_API_KEY是否正确设置
2. 确保API密钥格式正确（以sk-开头）
3. 确认密钥有足够的使用额度
4. 设置完成后重启开发服务器

### 生成失败
**问题**: 故事生成过程中出现错误
**解决**:
1. 检查网络连接
2. 确认OpenAI服务状态
3. 重试生成操作

### 内容解析错误
**问题**: 生成的内容无法正确解析
**解决**:
1. 重新生成故事
2. 尝试不同的主题
3. 检查API响应格式

## 📝 开发说明

### 核心文件
- `src/services/storyGeneratorService.js` - 故事生成服务
- `src/services/storyManager.js` - 故事管理器
- `src/services/promptTemplates.js` - 提示词模板
- `src/components/StoryGenerator.tsx` - 生成器UI组件
- `src/components/StorySelector.tsx` - 选择器UI组件

### 扩展主题
要添加新的故事主题：
1. 在 `promptTemplates.js` 中添加新的主题模板
2. 更新 `storyGeneratorService.js` 中的主题列表
3. 添加主题描述和颜色配置

### API集成
项目使用标准的OpenAI API：
- 模型：GPT-4o
- 温度：0.7
- 最大令牌：3000

## 🎯 使用建议

### 为儿童选择主题
- **人际关系**: 适合需要提升社交能力的儿童
- **家庭生活**: 适合加强家庭观念的儿童
- **法律常识**: 适合培养规则意识的儿童
- **人伦道德**: 适合品德教育的儿童

### 阅读指导
1. 陪伴儿童一起阅读
2. 鼓励儿童参与交互环节
3. 根据评估报告调整教育方向
4. 定期生成新故事保持新鲜感

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 检查网络连接和API服务状态
3. 参考本文档的故障排除部分
4. 联系技术支持团队

---

**注意**: 使用本功能需要有效的OpenAI API密钥，请确保您的账户有足够的使用额度。
