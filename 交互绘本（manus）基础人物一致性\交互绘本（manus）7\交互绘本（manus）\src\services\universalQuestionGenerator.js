/**
 * 通用交互问题生成服务
 * 基于页面内容分析自动生成适合自闭症儿童的交互问题
 */

import {
  generateInteractiveQuestionSet,
  analyzePageContent,
  selectInteractiveQuestion,
  UNIVERSAL_OBSERVATION_QUESTIONS,
  THEME_GUIDED_QUESTIONS,
  CREATIVE_EXPRESSION_QUESTIONS
} from './promptTemplates.js';

/**
 * 通用交互问题生成器
 * 不依赖具体故事内容，基于页面元素分析和主题模板匹配
 */
class UniversalQuestionGenerator {
  constructor() {
    this.questionHistory = new Map(); // 记录已使用的问题，避免重复
    this.adaptiveSettings = {
      complexityLevel: 'medium', // 'simple', 'medium', 'complex'
      preferredThemes: [], // 用户偏好的主题
      responseQuality: new Map() // 记录问题类型的回答质量
    };
  }

  /**
   * 为页面生成交互问题
   * @param {Object} pageData - 页面数据
   * @param {string} pageContent - 页面内容
   * @param {Object} options - 生成选项
   * @returns {Array} 生成的问题数组
   */
  generateQuestionsForPage(pageData, pageContent, options = {}) {
    console.log('🎯 开始生成通用交互问题...');
    console.log('📄 页面数据:', pageData);
    console.log('📝 页面内容:', pageContent);

    const {
      questionCount = 2, // 默认生成2个问题
      avoidRepetition = true,
      adaptToChild = true
    } = options;

    // 分析页面内容
    const analysis = analyzePageContent(pageData, pageContent);
    console.log('📊 页面分析结果:', analysis);

    // 生成问题集合
    const questions = [];
    const usedQuestionTypes = new Set();

    for (let i = 0; i < questionCount; i++) {
      let question;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        question = selectInteractiveQuestion(pageData, pageContent, i);
        attempts++;
      } while (
        avoidRepetition && 
        usedQuestionTypes.has(question.type + question.question) && 
        attempts < maxAttempts
      );

      if (question) {
        // 添加额外的元数据
        question.id = i + 1;
        question.pageId = pageData?.id || `page_${Date.now()}`;
        question.generatedAt = new Date().toISOString();
        question.difficulty = this.assessQuestionDifficulty(question);
        
        questions.push(question);
        usedQuestionTypes.add(question.type + question.question);

        // 记录问题历史
        if (avoidRepetition) {
          this.recordQuestionUsage(pageData?.id, question);
        }
      }
    }

    console.log('✅ 生成的问题集合:', questions);
    return questions;
  }

  /**
   * 评估问题难度
   * @param {Object} question - 问题对象
   * @returns {string} 难度等级
   */
  assessQuestionDifficulty(question) {
    const questionText = question.question.toLowerCase();
    
    // 简单问题：基础观察
    if (question.type === 'observation' || questionText.includes('看到')) {
      return 'simple';
    }
    
    // 复杂问题：创造性表达
    if (question.type === 'creative_expression' || questionText.includes('想象')) {
      return 'complex';
    }
    
    // 中等问题：主题引导
    return 'medium';
  }

  /**
   * 记录问题使用情况
   * @param {string} pageId - 页面ID
   * @param {Object} question - 问题对象
   */
  recordQuestionUsage(pageId, question) {
    if (!this.questionHistory.has(pageId)) {
      this.questionHistory.set(pageId, []);
    }
    this.questionHistory.get(pageId).push({
      question: question.question,
      type: question.type,
      usedAt: new Date().toISOString()
    });
  }

  /**
   * 根据回答质量调整问题生成策略
   * @param {string} questionType - 问题类型
   * @param {string} userResponse - 用户回答
   * @param {number} responseQuality - 回答质量评分 (1-5)
   */
  recordResponseQuality(questionType, userResponse, responseQuality) {
    if (!this.adaptiveSettings.responseQuality.has(questionType)) {
      this.adaptiveSettings.responseQuality.set(questionType, []);
    }
    
    this.adaptiveSettings.responseQuality.get(questionType).push({
      response: userResponse,
      quality: responseQuality,
      timestamp: new Date().toISOString()
    });

    // 自动调整偏好
    this.updatePreferences();
  }

  /**
   * 更新问题生成偏好
   */
  updatePreferences() {
    const qualityMap = this.adaptiveSettings.responseQuality;
    const themeScores = {};

    // 计算各主题的平均质量分数
    for (const [questionType, responses] of qualityMap.entries()) {
      if (responses.length > 0) {
        const avgQuality = responses.reduce((sum, r) => sum + r.quality, 0) / responses.length;
        themeScores[questionType] = avgQuality;
      }
    }

    // 更新偏好主题（选择高质量回答的主题）
    this.adaptiveSettings.preferredThemes = Object.entries(themeScores)
      .filter(([_, score]) => score >= 3.5)
      .map(([theme, _]) => theme);

    console.log('🔄 更新的偏好设置:', this.adaptiveSettings);
  }

  /**
   * 生成引导性追问
   * @param {string} originalQuestion - 原始问题
   * @param {string} userResponse - 用户回答
   * @returns {Object} 追问对象
   */
  generateFollowUpQuestion(originalQuestion, userResponse) {
    const responseLength = userResponse.trim().length;
    
    if (responseLength < 10) {
      // 回答太简短，提供具体引导
      return {
        question: "你能告诉我更多细节吗？比如你看到了什么颜色、什么形状的东西？",
        guidance_prompt: "试着用更多的词语来描述你看到的东西，比如'我看到了红色的...'",
        type: "elaboration"
      };
    } else if (responseLength < 30) {
      // 回答中等，鼓励扩展
      return {
        question: "这很有趣！你还能说说这让你想到了什么吗？",
        guidance_prompt: "你可以说说这个场景让你想起了什么经历或者感受。",
        type: "expansion"
      };
    } else {
      // 回答详细，给予肯定并深入
      return {
        question: "你观察得很仔细！如果你是图片中的角色，你会有什么感觉？",
        guidance_prompt: "想象一下如果你就在那个场景里，你会开心吗？紧张吗？还是其他感觉？",
        type: "empathy"
      };
    }
  }

  /**
   * 获取问题使用统计
   * @returns {Object} 统计信息
   */
  getUsageStatistics() {
    const stats = {
      totalQuestionsGenerated: 0,
      questionTypeDistribution: {},
      averageResponseQuality: 0,
      preferredThemes: this.adaptiveSettings.preferredThemes
    };

    // 统计问题类型分布
    for (const [pageId, questions] of this.questionHistory.entries()) {
      stats.totalQuestionsGenerated += questions.length;
      
      questions.forEach(q => {
        stats.questionTypeDistribution[q.type] = 
          (stats.questionTypeDistribution[q.type] || 0) + 1;
      });
    }

    // 计算平均回答质量
    let totalQuality = 0;
    let totalResponses = 0;
    
    for (const [_, responses] of this.adaptiveSettings.responseQuality.entries()) {
      responses.forEach(r => {
        totalQuality += r.quality;
        totalResponses++;
      });
    }
    
    if (totalResponses > 0) {
      stats.averageResponseQuality = totalQuality / totalResponses;
    }

    return stats;
  }

  /**
   * 重置生成器状态
   */
  reset() {
    this.questionHistory.clear();
    this.adaptiveSettings.responseQuality.clear();
    this.adaptiveSettings.preferredThemes = [];
    console.log('🔄 通用问题生成器已重置');
  }
}

// 创建全局实例
const universalQuestionGenerator = new UniversalQuestionGenerator();

export default universalQuestionGenerator;
export { UniversalQuestionGenerator };
