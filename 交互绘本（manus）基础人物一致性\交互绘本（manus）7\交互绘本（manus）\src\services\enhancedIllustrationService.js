/**
 * 增强版插画生成服务 - 集成风格一致性策略
 * 基于 style control.txt 文档的策略实现
 * 注意：保持现有的liblibai API调用代码不变，只增强提示词生成和一致性管理
 */

import liblibService from './liblibService.js';
import EnhancedPromptManager from './enhancedPromptManager.js';
import ConsistencyChecker from './consistencyChecker.js';
import CharacterManager from './characterManager.js';
import { generateSmartKeywords } from './promptTemplates.js';

class EnhancedIllustrationService {
    constructor() {
        this.promptManager = new EnhancedPromptManager();
        this.consistencyChecker = new ConsistencyChecker();
        this.characterManager = new CharacterManager();
        
        // 生成历史记录
        this.generationHistory = [];
        
        // 角色参考图像缓存
        this.characterReferences = new Map();
        
        // 当前故事上下文
        this.currentStoryContext = null;
    }

    /**
     * 设置故事上下文
     * @param {Object} storyContext - 故事上下文
     */
    setStoryContext(storyContext) {
        this.currentStoryContext = storyContext;
        this.promptManager.setStoryContext(storyContext);
    }

    /**
     * 生成页面插画（增强版）
     * @param {Object} pageInfo - 页面信息
     * @returns {Promise<string>} 生成的图像URL
     */
    async generatePageIllustration(pageInfo) {
        try {
            const {
                pageNumber,
                content,
                sceneType = "forest",
                characters = [],
                isInteractive = false,
                userAnswer = null
            } = pageInfo;

            console.log(`🎨 开始生成第${pageNumber}页插画，场景类型: ${sceneType}`);

            // 1. 检查是否有OpenAI生成的图像关键词
            const storyData = pageInfo.storyData;
            const storyPage = storyData?.pages?.find(p => p.id === pageNumber);
            const hasOpenAIKeywords = !!storyPage?.image_keywords;

            // 只有在没有OpenAI关键词时才生成角色种子图像
            if (!hasOpenAIKeywords) {
                console.log('⚠️ 未找到OpenAI关键词，生成角色种子图像...');
                await this.ensureCharacterReferences(characters);
            } else {
                console.log('✅ 找到OpenAI关键词，跳过角色种子图像生成');
            }

            // 2. 生成增强的提示词
            const enhancedPrompt = this.promptManager.generateEnhancedImagePrompt(pageInfo);
            
            console.log('🔧 增强提示词:', enhancedPrompt);

            // 3. 确定是否使用参考图像
            let referenceImageUrl = null;

            if (hasOpenAIKeywords) {
                // 使用OpenAI关键词时，优先使用历史页面作为参考
                referenceImageUrl = this.selectBestReferenceImage(characters, pageNumber, sceneType);
                if (referenceImageUrl) {
                    console.log('📸 使用历史页面作为参考图像:', referenceImageUrl);
                } else {
                    console.log('ℹ️ 使用OpenAI关键词进行纯文本生成');
                }
            } else {
                // 传统模式：检查是否应该使用参考图像
                const shouldUseReference = this.shouldUseReferenceImage(pageNumber, characters);
                if (shouldUseReference) {
                    referenceImageUrl = this.selectBestReferenceImage(characters, pageNumber, sceneType);
                    console.log('📸 使用参考图像:', referenceImageUrl);
                }
            }

            // 4. 调用现有的liblibai API（保持原有代码不变）
            let generatedImageUrl;
            if (referenceImageUrl) {
                // 使用image2image功能
                generatedImageUrl = await liblibService.generateImageFromImage(
                    referenceImageUrl,
                    enhancedPrompt.prompt,
                    '6-8岁'
                );
            } else {
                // 使用text2image功能
                generatedImageUrl = await liblibService.generateImage(
                    enhancedPrompt.prompt,
                    '6-8岁'
                );
            }

            // 5. 一致性检查
            const consistencyResult = await this.consistencyChecker.checkImageConsistency(
                generatedImageUrl,
                characters
            );

            console.log('✅ 一致性检查结果:', consistencyResult);

            // 6. 如果一致性不足，尝试重新生成（最多2次）
            if (!consistencyResult.isConsistent && consistencyResult.score < 0.6) {
                console.log('⚠️ 一致性不足，尝试重新生成...');
                
                // 调整提示词并重新生成
                const adjustedPrompt = this.adjustPromptForConsistency(enhancedPrompt, consistencyResult);
                
                if (referenceImageUrl) {
                    generatedImageUrl = await liblibService.generateImageFromImage(
                        referenceImageUrl,
                        adjustedPrompt.prompt,
                        '6-8岁'
                    );
                } else {
                    generatedImageUrl = await liblibService.generateImage(
                        adjustedPrompt.prompt,
                        '6-8岁'
                    );
                }
            }

            // 7. 更新角色参考图像
            this.updateCharacterReferences(characters, generatedImageUrl);

            // 8. 记录生成历史
            this.recordGenerationHistory({
                pageNumber,
                imageUrl: generatedImageUrl,
                prompt: enhancedPrompt.prompt,
                characters,
                sceneType,
                consistencyScore: consistencyResult.score,
                usedReference: !!referenceImageUrl,
                referenceImageUrl,
                content: pageInfo.content // 添加页面内容用于后续分析
            });

            console.log(`✨ 第${pageNumber}页插画生成完成:`, generatedImageUrl);
            return generatedImageUrl;

        } catch (error) {
            console.error(`❌ 第${pageInfo.pageNumber}页插画生成失败:`, error);
            throw error;
        }
    }

    /**
     * 生成个性化交互插画
     * @param {Object} interactionInfo - 交互信息
     * @returns {Promise<string>} 生成的图像URL
     */
    async generatePersonalizedIllustration(interactionInfo) {
        try {
            const {
                userAnswer,
                questionContext,
                pageNumber,
                baseCharacters = [],
                referenceImages = [],
                storyData = null,
                userEvents = []
            } = interactionInfo;

            console.log(`🎭 生成个性化插画，基于用户回答: "${userAnswer}"`);
            console.log(`🎬 用户事件: ${userEvents.join(', ')}`);

            // 检查是否有OpenAI生成的图像关键词
            const storyPage = storyData?.pages?.find(p => p.id === pageNumber);
            const hasOpenAIKeywords = !!storyPage?.image_keywords;

            let personalizedPrompt;

            if (hasOpenAIKeywords && storyData?.characters) {
                console.log('✅ 使用OpenAI生成的角色信息和关键词');

                // 使用OpenAI生成的角色描述
                const characterDescriptions = storyData.characters.reduce((acc, char) => {
                    acc[char.name] = char.description;
                    return acc;
                }, {});

                // 构建基于OpenAI数据的提示词
                personalizedPrompt = this.buildOpenAIBasedPrompt({
                    userAnswer,
                    userEvents,
                    pageNumber,
                    imageKeywords: storyPage.image_keywords,
                    characterDescriptions,
                    baseCharacters
                });
            } else {
                console.log('⚠️ 使用传统方法生成个性化提示词');

                // 传统方法
                personalizedPrompt = this.promptManager.generatePersonalizedIllustrationPrompt({
                    userAnswer,
                    questionContext,
                    pageNumber,
                    baseCharacters,
                    emotion: "happy",
                    userEvents
                });
            }

            console.log('🎨 个性化提示词:', personalizedPrompt);

            // 选择最佳参考图像
            const referenceImageUrl = referenceImages.length > 0 ?
                referenceImages[0].url || referenceImages[0] :
                this.selectBestReferenceImage(baseCharacters, pageNumber);

            let generatedImageUrl;

            if (referenceImageUrl) {
                console.log('📸 使用参考图像生成:', referenceImageUrl);
                // 使用image2image生成个性化插画
                generatedImageUrl = await liblibService.generateImageFromImage(
                    referenceImageUrl,
                    personalizedPrompt.prompt || personalizedPrompt,
                    '6-8岁'
                );
            } else {
                console.log('📝 使用纯文本生成');
                // 使用text2image生成
                generatedImageUrl = await liblibService.generateImage(
                    personalizedPrompt.prompt || personalizedPrompt,
                    '6-8岁'
                );
            }

            // 记录个性化生成历史
            this.recordGenerationHistory({
                pageNumber,
                imageUrl: generatedImageUrl,
                prompt: personalizedPrompt.prompt || personalizedPrompt,
                characters: baseCharacters,
                isPersonalized: true,
                userAnswer,
                userEvents,
                usedReference: !!referenceImageUrl,
                referenceImageUrl,
                hasOpenAIKeywords
            });

            console.log('✨ 个性化插画生成完成:', generatedImageUrl);
            return generatedImageUrl;

        } catch (error) {
            console.error('❌ 个性化插画生成失败:', error);
            throw error;
        }
    }

    /**
     * 构建基于OpenAI数据的提示词（使用智能关键词系统）
     * @param {Object} params - 参数对象
     * @returns {Object} 提示词对象
     */
    buildOpenAIBasedPrompt(params) {
        const {
            userAnswer,
            userEvents,
            pageNumber,
            imageKeywords,
            characterDescriptions,
            baseCharacters
        } = params;

        console.log('🔧 构建基于OpenAI数据的智能关键词提示词...');

        // 获取主要角色数据
        let mainCharacterData = null;
        if (baseCharacters && baseCharacters.length > 0) {
            const firstChar = baseCharacters[0];
            const charName = typeof firstChar === 'string' ? firstChar : firstChar.name;
            mainCharacterData = {
                name: charName,
                description: firstChar.description || characterDescriptions[charName] || ''
            };
        }

        console.log('🎭 主要角色数据:', mainCharacterData);

        // 确定场景
        let scene = 'in a beautiful garden with flowers and trees';
        if (imageKeywords.scene) {
            const sceneText = imageKeywords.scene.toLowerCase();
            if (sceneText.includes('教室') || sceneText.includes('classroom')) {
                scene = 'in a bright classroom with books and desks';
            } else if (sceneText.includes('公园') || sceneText.includes('park')) {
                scene = 'in a sunny park with playground equipment';
            } else if (sceneText.includes('家') || sceneText.includes('home')) {
                scene = 'in a cozy home interior with warm lighting';
            } else if (sceneText.includes('森林') || sceneText.includes('forest')) {
                scene = 'in a magical forest with tall trees and sunlight';
            }
        }

        // 确定情绪
        let emotion = 'happy';
        if (imageKeywords.mood) {
            const moodText = imageKeywords.mood.toLowerCase();
            if (moodText.includes('好奇') || moodText.includes('curious')) {
                emotion = 'curious';
            } else if (moodText.includes('平静') || moodText.includes('peaceful')) {
                emotion = 'peaceful';
            } else if (moodText.includes('思考') || moodText.includes('thinking')) {
                emotion = 'thoughtful';
            }
        }

        // 使用智能关键词生成系统
        const smartKeywords = generateSmartKeywords(
            userAnswer,
            mainCharacterData,
            scene
        );

        console.log('🎯 生成的智能关键词:', smartKeywords);
        console.log('✅ OpenAI基础智能关键词构建完成');
        return { prompt: smartKeywords };
    }

    /**
     * 确保角色有参考图像
     * @param {Array} characters - 角色列表
     */
    async ensureCharacterReferences(characters) {
        for (const character of characters) {
            const characterName = typeof character === 'string' ? character : character.name;
            
            if (!this.consistencyChecker.hasCharacterReference(characterName)) {
                console.log(`🎭 为角色 ${characterName} 生成种子图像...`);
                
                try {
                    const seedPrompt = this.promptManager.generateCharacterSeedPrompt(characterName);
                    const seedImageUrl = await liblibService.generateImage(seedPrompt.prompt, '6-8岁');
                    
                    // 设置为角色参考图像
                    this.consistencyChecker.setCharacterReference(characterName, seedImageUrl, {
                        isSeedImage: true,
                        prompt: seedPrompt.prompt
                    });
                    
                    this.characterReferences.set(characterName, seedImageUrl);
                    
                    console.log(`✅ 角色 ${characterName} 种子图像生成完成:`, seedImageUrl);
                } catch (error) {
                    console.error(`❌ 角色 ${characterName} 种子图像生成失败:`, error);
                }
            }
        }
    }

    /**
     * 判断是否应该使用参考图像
     * @param {number} pageNumber - 页面号
     * @param {Array} characters - 角色列表
     * @returns {boolean} 是否使用参考图像
     */
    shouldUseReferenceImage(pageNumber, characters) {
        // 第一页通常不使用参考图像（除非有种子图像）
        if (pageNumber === 1) {
            return characters.some(char => {
                const characterName = typeof char === 'string' ? char : char.name;
                return this.characterReferences.has(characterName);
            });
        }
        
        // 其他页面优先使用参考图像保持一致性
        return this.generationHistory.length > 0 || this.characterReferences.size > 0;
    }

    /**
     * 选择最佳参考图像（取消种子图像依赖）
     * @param {Array} characters - 角色列表
     * @param {number} pageNumber - 页面号
     * @param {string} sceneType - 场景类型
     * @returns {string|null} 参考图像URL
     */
    selectBestReferenceImage(characters, pageNumber, sceneType = null) {
        console.log(`🔍 为第${pageNumber}页选择最佳参考图像...`);
        console.log(`📋 角色列表:`, characters.map(c => typeof c === 'string' ? c : c.name));
        console.log(`🎭 场景类型:`, sceneType);

        // 1. 优先使用相似场景的最近页面图像
        if (this.generationHistory.length > 0 && sceneType) {
            const similarSceneHistory = this.generationHistory
                .filter(h => h.pageNumber < pageNumber && h.sceneType === sceneType)
                .sort((a, b) => b.pageNumber - a.pageNumber);

            if (similarSceneHistory.length > 0) {
                console.log(`✅ 使用相似场景 ${sceneType} 的页面图像: 第${similarSceneHistory[0].pageNumber}页`);
                return similarSceneHistory[0].imageUrl;
            }
        }

        // 2. 使用最近的任何页面图像
        if (this.generationHistory.length > 0) {
            const recentHistory = this.generationHistory
                .filter(h => h.pageNumber < pageNumber)
                .sort((a, b) => b.pageNumber - a.pageNumber);

            if (recentHistory.length > 0) {
                console.log(`✅ 使用最近的页面图像: 第${recentHistory[0].pageNumber}页`);
                return recentHistory[0].imageUrl;
            }
        }

        // 3. 如果是第一页或没有历史记录，不使用参考图像
        console.log(`ℹ️ 第${pageNumber}页无可用参考图像，将进行纯文本生成`);
        return null;
    }

    /**
     * 调整提示词以提高一致性
     * @param {Object} originalPrompt - 原始提示词
     * @param {Object} consistencyResult - 一致性检查结果
     * @returns {Object} 调整后的提示词
     */
    adjustPromptForConsistency(originalPrompt, consistencyResult) {
        const consistencyEnhancements = [
            "consistent character design",
            "same art style",
            "matching visual style",
            "coherent illustration style"
        ];

        const adjustedPrompt = {
            ...originalPrompt,
            prompt: `${originalPrompt.prompt}, ${consistencyEnhancements.join(", ")}`
        };

        return adjustedPrompt;
    }

    /**
     * 更新角色参考图像
     * @param {Array} characters - 角色列表
     * @param {string} imageUrl - 图像URL
     */
    updateCharacterReferences(characters, imageUrl) {
        for (const character of characters) {
            const characterName = typeof character === 'string' ? character : character.name;
            
            // 如果角色还没有参考图像，使用当前图像
            if (!this.characterReferences.has(characterName)) {
                this.characterReferences.set(characterName, imageUrl);
                this.consistencyChecker.setCharacterReference(characterName, imageUrl);
            }
        }
    }

    /**
     * 记录生成历史
     * @param {Object} historyEntry - 历史记录条目
     */
    recordGenerationHistory(historyEntry) {
        this.generationHistory.push({
            ...historyEntry,
            timestamp: Date.now()
        });

        // 保持最近20条记录
        if (this.generationHistory.length > 20) {
            this.generationHistory = this.generationHistory.slice(-20);
        }
    }

    /**
     * 获取生成统计信息
     * @returns {Object} 统计信息
     */
    getGenerationStats() {
        const totalGenerated = this.generationHistory.length;
        const personalizedCount = this.generationHistory.filter(h => h.isPersonalized).length;
        const withReferenceCount = this.generationHistory.filter(h => h.usedReference).length;
        const averageConsistency = this.generationHistory.length > 0 ?
            this.generationHistory.reduce((sum, h) => sum + (h.consistencyScore || 0), 0) / totalGenerated : 0;

        return {
            totalGenerated,
            personalizedCount,
            withReferenceCount,
            averageConsistency: Math.round(averageConsistency * 100) / 100,
            characterReferencesCount: this.characterReferences.size,
            qualityReport: this.consistencyChecker.getQualityReport()
        };
    }

    /**
     * 重置服务状态
     */
    reset() {
        this.generationHistory = [];
        this.characterReferences.clear();
        this.consistencyChecker.reset();
        this.currentStoryContext = null;
        console.log('🔄 增强插画服务已重置');
    }
}

// 创建单例实例
const enhancedIllustrationService = new EnhancedIllustrationService();

export default enhancedIllustrationService;
