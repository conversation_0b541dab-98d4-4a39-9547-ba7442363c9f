/**
 * 场景风格控制器 - 统一管理场景风格和角色组合
 * 基于 style control.txt 文档的策略实现
 * 注意：不涉及API调用，只负责场景风格控制和提示词生成
 */

import StyleManager from './styleManager.js';
import CharacterManager from './characterManager.js';

class SceneStyleController {
    constructor() {
        this.styleManager = new StyleManager();
        this.characterManager = new CharacterManager();
        
        // 场景模板库
        this.sceneTemplates = {
            forest: {
                environment: "peaceful forest clearing",
                colors: "green and brown tones",
                elements: "trees, grass, flowers, soft sunlight",
                mood: "natural, peaceful",
                lighting: "dappled sunlight through trees"
            },
            indoor: {
                environment: "cozy indoor space",
                colors: "warm interior colors",
                elements: "furniture, books, toys, warm lighting",
                mood: "comfortable, safe",
                lighting: "warm indoor lighting"
            },
            playground: {
                environment: "safe playground area",
                colors: "bright, playful colors",
                elements: "playground equipment, grass, blue sky",
                mood: "playful, energetic",
                lighting: "bright daylight"
            },
            home: {
                environment: "comfortable home setting",
                colors: "homey, familiar colors",
                elements: "family furniture, personal items, cozy atmosphere",
                mood: "familiar, secure",
                lighting: "warm home lighting"
            },
            interaction: {
                environment: "neutral background",
                colors: "soft, non-distracting colors",
                elements: "simple background, focus on characters",
                mood: "focused, clear",
                lighting: "even, clear lighting"
            }
        };
    }

    /**
     * 根据场景类型生成场景相关的提示词
     * @param {string} sceneType - 场景类型
     * @param {Array} characters - 角色信息数组
     * @param {string} actionDescription - 动作描述
     * @param {Object} options - 额外选项
     * @returns {Object} 完整的场景提示词
     */
    generateScenePrompt(sceneType, characters = [], actionDescription = "", options = {}) {
        const sceneTemplate = this.sceneTemplates[sceneType] || this.sceneTemplates.forest;
        
        // 生成角色描述
        let characterDescription = "";
        if (characters.length > 0) {
            characterDescription = this.characterManager.generateMultiCharacterDescription(characters);
        }

        // 构建完整场景描述
        const sceneElements = [
            characterDescription,
            actionDescription,
            `in ${sceneTemplate.environment}`,
            sceneTemplate.colors,
            sceneTemplate.elements,
            sceneTemplate.mood,
            sceneTemplate.lighting
        ].filter(Boolean);

        const fullSceneDescription = sceneElements.join(", ");

        // 应用情绪调整
        let emotionAdjustment = {};
        if (characters.length > 0 && characters[0].emotion) {
            emotionAdjustment = this.styleManager.getEmotionStyleAdjustment(characters[0].emotion);
        }

        // 生成最终提示词
        const basePrompt = this.styleManager.generateStylePrompt(fullSceneDescription, options);

        // 如果有情绪调整，融入到提示词中
        if (Object.keys(emotionAdjustment).length > 0) {
            const emotionEnhancedPrompt = this.enhancePromptWithEmotion(basePrompt.prompt, emotionAdjustment);
            basePrompt.prompt = emotionEnhancedPrompt;
        }

        return {
            ...basePrompt,
            sceneInfo: {
                sceneType,
                environment: sceneTemplate.environment,
                mood: sceneTemplate.mood,
                characters: characters.map(c => c.name),
                hasEmotion: !!emotionAdjustment.moodAdjustment
            }
        };
    }

    /**
     * 为交互页面生成特殊场景提示词
     * @param {string} interactionType - 交互类型 (question, choice, activity)
     * @param {Array} characters - 角色信息数组
     * @param {string} interactionContext - 交互上下文
     * @param {Object} options - 额外选项
     * @returns {Object} 交互场景提示词
     */
    generateInteractiveScenePrompt(interactionType, characters = [], interactionContext = "", options = {}) {
        const interactiveStyle = this.styleManager.getInteractivePageStyle(interactionType);
        
        // 生成角色描述
        let characterDescription = "";
        if (characters.length > 0) {
            characterDescription = this.characterManager.generateMultiCharacterDescription(characters);
        }

        // 构建交互场景描述
        const interactiveElements = [
            characterDescription,
            interactionContext,
            interactiveStyle.background,
            interactiveStyle.focus,
            interactiveStyle.colors,
            interactiveStyle.composition
        ].filter(Boolean);

        const fullInteractiveDescription = interactiveElements.join(", ");

        // 添加交互特定的负面提示词
        const interactiveNegative = [
            "busy background",
            "distracting elements",
            "complex composition",
            "overwhelming details"
        ];

        const enhancedOptions = {
            ...options,
            additionalNegative: [...(options.additionalNegative || []), ...interactiveNegative]
        };

        const basePrompt = this.styleManager.generateStylePrompt(fullInteractiveDescription, enhancedOptions);

        return {
            ...basePrompt,
            interactionInfo: {
                interactionType,
                style: interactiveStyle,
                characters: characters.map(c => c.name),
                isInteractive: true
            }
        };
    }

    /**
     * 根据故事页面生成场景提示词
     * @param {Object} pageInfo - 页面信息
     * @returns {Object} 页面场景提示词
     */
    generatePageScenePrompt(pageInfo) {
        const {
            pageNumber,
            sceneType = "forest",
            characters = [],
            actions = "",
            isInteractive = false,
            interactionType = "question",
            emotion = "neutral"
        } = pageInfo;

        // 为角色添加适当的情绪
        const enhancedCharacters = characters.map(char => ({
            ...char,
            emotion: char.emotion || this.characterManager.getSceneAppropriateEmotion(sceneType, char.name)
        }));

        // 根据是否为交互页面选择生成方法
        if (isInteractive) {
            return this.generateInteractiveScenePrompt(
                interactionType,
                enhancedCharacters,
                actions,
                { pageNumber }
            );
        } else {
            return this.generateScenePrompt(
                sceneType,
                enhancedCharacters,
                actions,
                { pageNumber }
            );
        }
    }

    /**
     * 使用情绪调整增强提示词
     * @param {string} basePrompt - 基础提示词
     * @param {Object} emotionAdjustment - 情绪调整参数
     * @returns {string} 增强后的提示词
     */
    enhancePromptWithEmotion(basePrompt, emotionAdjustment) {
        const emotionElements = [
            emotionAdjustment.colorAdjustment,
            emotionAdjustment.lightingAdjustment,
            emotionAdjustment.moodAdjustment
        ].filter(Boolean);

        if (emotionElements.length > 0) {
            return `${basePrompt}, ${emotionElements.join(", ")}`;
        }

        return basePrompt;
    }

    /**
     * 获取场景的推荐角色配置
     * @param {string} sceneType - 场景类型
     * @returns {Object} 推荐的角色配置
     */
    getRecommendedCharacterSetup(sceneType) {
        const recommendations = {
            forest: {
                mainCharacter: "小熊波波",
                emotion: "curious",
                action: "exploring",
                companions: ["小兔子"]
            },
            playground: {
                mainCharacter: "小熊波波",
                emotion: "happy",
                action: "playing",
                companions: ["小兔子", "小松鼠"]
            },
            indoor: {
                mainCharacter: "小熊波波",
                emotion: "calm",
                action: "reading",
                companions: []
            },
            interaction: {
                mainCharacter: "小熊波波",
                emotion: "shy",
                action: "thinking",
                companions: ["小兔子"]
            }
        };

        return recommendations[sceneType] || recommendations.forest;
    }

    /**
     * 验证场景配置的合理性
     * @param {string} sceneType - 场景类型
     * @param {Array} characters - 角色配置
     * @returns {Object} 验证结果
     */
    validateSceneConfiguration(sceneType, characters) {
        const issues = [];
        const suggestions = [];

        // 检查角色数量
        if (characters.length === 0) {
            issues.push("场景中没有角色");
            suggestions.push("至少添加一个主要角色");
        }

        if (characters.length > 3) {
            issues.push("角色数量过多，可能影响画面清晰度");
            suggestions.push("考虑减少角色数量到3个以内");
        }

        // 检查场景与角色的匹配度
        const recommendation = this.getRecommendedCharacterSetup(sceneType);
        const hasMainCharacter = characters.some(c => c.name === recommendation.mainCharacter);
        
        if (!hasMainCharacter) {
            suggestions.push(`建议添加主角 ${recommendation.mainCharacter}`);
        }

        return {
            isValid: issues.length === 0,
            issues,
            suggestions,
            recommendation
        };
    }
}

export default SceneStyleController;
